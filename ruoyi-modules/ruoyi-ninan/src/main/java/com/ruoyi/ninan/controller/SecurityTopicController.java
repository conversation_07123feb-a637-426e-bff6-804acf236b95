package com.ruoyi.ninan.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.ninan.domain.UserInfo;
import com.ruoyi.ninan.service.IUserInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/securityTopic")
public class SecurityTopicController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(SecurityTopicController.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private IUserInfoService userInfoService;

    @Value("${wechat.mp.appid}")
    private String appid;

    @Value("${wechat.mp.secret}")
    private String appSecret;

    // 常量定义
    private static final String USER_STATUS_REGISTERED = "1";
    private static final String USER_STATUS_UNREGISTERED = "2";
    private static final String INITIAL_LEARNING_STATUS = "0";
    private static final Long COMPLETED_LEARNING_STATUS = 3L;
    private static final int CACHE_EXPIRE_MINUTES = 60;
    private static final int UNIONID_CACHE_EXPIRE_MINUTES = 600;

    /**
     * 用户进入公众号授权
     * @param code 微信授权码
     * @return 用户信息或授权结果
     */
    @GetMapping("/analyzeCode")
    public AjaxResult analyzeCode(@RequestParam String code) {
        if (code == null || code.trim().isEmpty()) {
            return error("授权码不能为空");
        }

        try {
            // 检查缓存中是否存在unionId
            if (redisService.hasKey(code)) {
                return handleCachedUnionId(code);
            } else {
                return handleNewAuthorization(code);
            }
        } catch (Exception e) {
            log.error("处理微信授权失败: {}", e.getMessage(), e);
            return error("授权处理失败，请重试");
        }
    }

    /**
     * 处理缓存中的unionId
     */
    private AjaxResult handleCachedUnionId(String code) {
        String unionId = redisService.getCacheObject(code);
        UserInfo userInfo = userInfoService.selectUserInfoByUnionid(unionId);

        if (userInfo == null) {
            log.warn("缓存的unionId对应的用户不存在: {}", unionId);
            return buildUnregisteredResponse();
        }

        // 延长缓存时间
        redisService.expire(code, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        return buildUserResponse(userInfo, unionId);
    }

    /**
     * 处理新的授权请求
     */
    private AjaxResult handleNewAuthorization(String code) {
        String unionId = getUnionId(appid, appSecret, code);

        if (unionId == null || unionId.trim().isEmpty()) {
            log.warn("获取unionId失败，code: {}", code);
            return error("获取用户信息失败");
        }

        UserInfo userInfo = userInfoService.selectUserInfoByUnionid(unionId);

        if (userInfo != null) {
            // 缓存unionId
            redisService.setCacheObject(code, unionId, UNIONID_CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            return buildUserResponse(userInfo, unionId);
        } else {
            return buildUnregisteredResponse();
        }
    }

    /**
     * 构建已注册用户响应
     */
    private AjaxResult buildUserResponse(UserInfo userInfo, String unionId) {
        JSONObject json = new JSONObject();
        json.put("yhzt", USER_STATUS_REGISTERED);
        json.put("xm", userInfo.getXm());
        json.put("xxzt", userInfo.getXxzt());
        json.put("userId", userInfo.getId());
        json.put("unionid", unionId);
        return success(json);
    }

    /**
     * 构建未注册用户响应
     */
    private AjaxResult buildUnregisteredResponse() {
        JSONObject json = new JSONObject();
        json.put("yhzt", USER_STATUS_UNREGISTERED);
        return success(json);
    }

    /**
     * 更新用户学习进度
     * @param type 学习类型/进度
     * @param spId 视频ID
     * @param unionid 用户unionid
     * @return 更新结果
     */
    @GetMapping("/pass")
    public AjaxResult pass(@RequestParam Long type,
                          @RequestParam(required = false) Long spId,
                          @RequestParam String unionid) {
        // 参数验证
        if (type == null || unionid == null || unionid.trim().isEmpty()) {
            return error("参数不能为空");
        }

        try {
            UserInfo userInfo = userInfoService.selectUserInfoByUnionid(unionid);
            if (userInfo == null) {
                return error("用户不存在");
            }

            // 计算新的学习状态
            Long newLearningStatus = calculateLearningStatus(userInfo.getXxzt(), type);

            // 更新用户学习状态
            UserInfo updateUser = new UserInfo();
            updateUser.setId(userInfo.getId());
            updateUser.setXxzt(newLearningStatus);

            // TODO: 取消注释以启用数据库更新
            // userInfoService.updateUserInfo(updateUser);

            log.info("用户学习状态更新成功 - 用户ID: {}, 原状态: {}, 新状态: {}, 视频ID: {}",
                    userInfo.getId(), userInfo.getXxzt(), newLearningStatus, spId);

            return success(true);
        } catch (Exception e) {
            log.error("更新用户学习状态失败: {}", e.getMessage(), e);
            return error("更新失败，请重试");
        }
    }

    /**
     * 计算新的学习状态
     * @param currentStatus 当前学习状态
     * @param newType 新的学习类型
     * @return 计算后的学习状态
     */
    private Long calculateLearningStatus(Long currentStatus, Long newType) {
        // 如果是初始状态或者新类型等于当前状态，则使用新类型
        if (INITIAL_LEARNING_STATUS.equals(String.valueOf(currentStatus)) || newType.equals(currentStatus)) {
            return newType;
        } else {
            // 否则设置为完成状态
            return COMPLETED_LEARNING_STATUS;
        }
    }



    /**
     * 根据微信授权码获取用户unionId
     * @param appid 微信公众号appid
     * @param appsecret 微信公众号密钥
     * @param code 微信授权码
     * @return 用户unionId
     */
    private String getUnionId(String appid, String appsecret, String code) {
        if (appid == null || appsecret == null || code == null) {
            log.error("获取unionId参数不能为空");
            return null;
        }

        HttpURLConnection connection = null;
        BufferedReader reader = null;

        try {
            // 构建微信API URL
            String apiUrl = buildWechatApiUrl(appid, appsecret, code);
            URL url = new URL(apiUrl);

            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("微信API调用失败，响应码: {}", responseCode);
                return null;
            }

            // 读取响应内容
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            // 解析响应
            return parseUnionIdFromResponse(response.toString());

        } catch (IOException e) {
            log.error("调用微信API获取unionId失败: {}", e.getMessage(), e);
            return null;
        } finally {
            // 关闭资源
            closeResources(reader, connection);
        }
    }

    /**
     * 构建微信API URL
     */
    private String buildWechatApiUrl(String appid, String appsecret, String code) {
        return String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                appid, appsecret, code);
    }

    /**
     * 从微信API响应中解析unionId
     */
    private String parseUnionIdFromResponse(String response) {
        try {
            log.debug("微信API响应: {}", response);
            JSONObject jsonObject = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonObject.containsKey("errcode")) {
                log.error("微信API返回错误: {}", response);
                return null;
            }

            return jsonObject.getString("unionid");
        } catch (Exception e) {
            log.error("解析微信API响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 关闭网络连接资源
     */
    private void closeResources(BufferedReader reader, HttpURLConnection connection) {
        if (reader != null) {
            try {
                reader.close();
            } catch (IOException e) {
                log.warn("关闭BufferedReader失败: {}", e.getMessage());
            }
        }
        if (connection != null) {
            connection.disconnect();
        }
    }
}
