<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.JlyGpsMapper">
    
    <resultMap type="JlyGps" id="JlyGpsResult">
        <result property="id"    column="id"    />
        <result property="sbid"    column="sbid"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="ol"    column="ol"    />
        <result property="gt"    column="gt"    />
        <result property="net"    column="net"    />
        <result property="sydl"    column="sydl"    />
        <result property="formattedAddress"    column="formatted_address"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="town"    column="town"    />
        <result property="txlng"    column="txlng"    />
        <result property="txlat"    column="txlat"    />
    </resultMap>

    <sql id="selectJlyGpsVo">
        select id, sbid, lng, lat, ol, gt, net, sydl, formatted_address, province, city, district, town, txlng, txlat from jly_gps
    </sql>

    <select id="selectJlyGpsList" parameterType="JlyGps" resultMap="JlyGpsResult">
        <include refid="selectJlyGpsVo"/>
        <where>  
            <if test="sbid != null  and sbid != ''"> and sbid = #{sbid}</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="ol != null  and ol != ''"> and ol = #{ol}</if>
            <if test="gt != null  and gt != ''"> and gt = #{gt}</if>
            <if test="net != null  and net != ''"> and net = #{net}</if>
            <if test="sydl != null  and sydl != ''"> and sydl = #{sydl}</if>
            <if test="formattedAddress != null  and formattedAddress != ''"> and formatted_address = #{formattedAddress}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="town != null  and town != ''"> and town = #{town}</if>
            <if test="txlng != null  and txlng != ''"> and txlng = #{txlng}</if>
            <if test="txlat != null  and txlat != ''"> and txlat = #{txlat}</if>
        </where>
    </select>
    
    <select id="selectJlyGpsById" parameterType="Long" resultMap="JlyGpsResult">
        <include refid="selectJlyGpsVo"/>
        where id = #{id}
    </select>

    <select id="selectJlyGpsSbid" parameterType="String" resultMap="JlyGpsResult">
        <include refid="selectJlyGpsVo"/>
        where sbid = #{sbid}
    </select>
        
    <insert id="insertJlyGps" parameterType="JlyGps" useGeneratedKeys="true" keyProperty="id">
        insert into jly_gps
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sbid != null">sbid,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="ol != null">ol,</if>
            <if test="gt != null">gt,</if>
            <if test="net != null">net,</if>
            <if test="sydl != null">sydl,</if>
            <if test="formattedAddress != null">formatted_address,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="town != null">town,</if>
            <if test="txlng != null">txlng,</if>
            <if test="txlat != null">txlat,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sbid != null">#{sbid},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="ol != null">#{ol},</if>
            <if test="gt != null">#{gt},</if>
            <if test="net != null">#{net},</if>
            <if test="sydl != null">#{sydl},</if>
            <if test="formattedAddress != null">#{formattedAddress},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="town != null">#{town},</if>
            <if test="txlng != null">#{txlng},</if>
            <if test="txlat != null">#{txlat},</if>
         </trim>
    </insert>

    <update id="updateJlyGps" parameterType="JlyGps">
        update jly_gps
        <trim prefix="SET" suffixOverrides=",">
            <if test="sbid != null">sbid = #{sbid},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="ol != null">ol = #{ol},</if>
            <if test="gt != null">gt = #{gt},</if>
            <if test="net != null">net = #{net},</if>
            <if test="sydl != null">sydl = #{sydl},</if>
            <if test="formattedAddress != null">formatted_address = #{formattedAddress},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="town != null">town = #{town},</if>
            <if test="txlng != null">txlng = #{txlng},</if>
            <if test="txlat != null">txlat = #{txlat},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJlyGpsById" parameterType="Long">
        delete from jly_gps where id = #{id}
    </delete>

    <delete id="deleteJlyGpsByIds" parameterType="String">
        delete from jly_gps where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>