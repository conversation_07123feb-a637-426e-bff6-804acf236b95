<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserNhJyjlMapper">
    
    <resultMap type="UserNhJyjl" id="UserNhJyjlResult">
        <result property="id"    column="id"    />
        <result property="jyBh"    column="jy_bh"    />
        <result property="jyJe"    column="jy_je"    />
        <result property="jyDd"    column="jy_dd"    />
        <result property="dqYe"    column="dq_ye"    />
        <result property="jyLx"    column="jy_lx"    />
        <result property="userId"    column="user_id"    />
        <result property="cjsj"    column="cjsj"    />
    </resultMap>

    <sql id="selectUserNhJyjlVo">
        select id, jy_bh, jy_je, jy_dd, dq_ye, jy_lx, user_id, cjsj from user_nh_jyjl
    </sql>

    <select id="selectUserNhJyjlList" parameterType="UserNhJyjl" resultMap="UserNhJyjlResult">
        <include refid="selectUserNhJyjlVo"/>
        <where>  
            <if test="jyBh != null  and jyBh != ''"> and jy_bh = #{jyBh}</if>
            <if test="jyJe != null "> and jy_je = #{jyJe}</if>
            <if test="jyDd != null  and jyDd != ''"> and jy_dd = #{jyDd}</if>
            <if test="dqYe != null "> and dq_ye = #{dqYe}</if>
            <if test="jyLx != null  and jyLx != ''"> and jy_lx = #{jyLx}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
        </where>
        order by cjsj desc
    </select>
    
    <select id="selectUserNhJyjlById" parameterType="Long" resultMap="UserNhJyjlResult">
        <include refid="selectUserNhJyjlVo"/>
        where id = #{id}
    </select>

    <select id="selectUserNhJyjlByUserId" parameterType="Long" resultMap="UserNhJyjlResult">
        <include refid="selectUserNhJyjlVo"/>
        where user_id = #{userId}
        order by cjsj desc
    </select>
        
    <insert id="insertUserNhJyjl" parameterType="UserNhJyjl" useGeneratedKeys="true" keyProperty="id">
        insert into user_nh_jyjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jyBh != null">jy_bh,</if>
            <if test="jyJe != null">jy_je,</if>
            <if test="jyDd != null">jy_dd,</if>
            <if test="dqYe != null">dq_ye,</if>
            <if test="jyLx != null">jy_lx,</if>
            <if test="userId != null">user_id,</if>
            <if test="cjsj != null">cjsj,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jyBh != null">#{jyBh},</if>
            <if test="jyJe != null">#{jyJe},</if>
            <if test="jyDd != null">#{jyDd},</if>
            <if test="dqYe != null">#{dqYe},</if>
            <if test="jyLx != null">#{jyLx},</if>
            <if test="userId != null">#{userId},</if>
            <if test="cjsj != null">#{cjsj},</if>
         </trim>
    </insert>

    <update id="updateUserNhJyjl" parameterType="UserNhJyjl">
        update user_nh_jyjl
        <trim prefix="SET" suffixOverrides=",">
            <if test="jyBh != null">jy_bh = #{jyBh},</if>
            <if test="jyJe != null">jy_je = #{jyJe},</if>
            <if test="jyDd != null">jy_dd = #{jyDd},</if>
            <if test="dqYe != null">dq_ye = #{dqYe},</if>
            <if test="jyLx != null">jy_lx = #{jyLx},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserNhJyjlById" parameterType="Long">
        delete from user_nh_jyjl where id = #{id}
    </delete>

    <delete id="deleteUserNhJyjlByIds" parameterType="String">
        delete from user_nh_jyjl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
