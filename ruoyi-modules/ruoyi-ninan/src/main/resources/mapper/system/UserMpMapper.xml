<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserMpMapper">

    <resultMap id="UserInteractionResultMap" type="com.ruoyi.ninan.domain.UserMp">
        <result column="unionid" property="unionid"/>
        <result column="user_name" property="userName"/>
        <result column="gender" property="gender"/>
        <result column="id_number" property="idNumber"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="mp1_openid" property="mp1Openid"/>
        <result column="mp1_status" property="mp1Status"/>
        <result column="mp2_openid" property="mp2Openid"/>
        <result column="mp2_status" property="mp2Status"/>
        <result column="miniapp1_openid" property="miniapp1Openid"/>
        <result column="miniapp2_openid" property="miniapp2Openid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 根据用户唯一标识查询用户交互信息 -->
    <select id="findByUnionid" parameterType="string" resultMap="UserInteractionResultMap">
        SELECT unionid, user_name, gender, id_number, phone_number,
               mp1_openid, mp1_status, mp2_openid, mp2_status,
               miniapp1_openid, miniapp2_openid, created_at, updated_at
        FROM user_mp
        WHERE unionid = #{unionid}
    </select>


    <select id="existsUnionid" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM user_mp
            WHERE unionid = #{unionid}
        )
    </select>



    <select id="findBymp1Openid" parameterType="string" resultType="string">
        SELECT unionid
        FROM user_mp
        WHERE
         mp1_openid = #{mp1Openid}
    </select>

    <select id="findBymp2Openid" parameterType="string" resultType="string">
        SELECT unionid
        FROM user_mp
        WHERE
         mp2_openid = #{mp2Openid}
    </select>

    <!-- 插入用户交互信息 -->
    <insert id="insertUserInteraction" parameterType="com.ruoyi.ninan.domain.UserMp" >
        INSERT INTO user_mp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unionid != null"> unionid, </if>
            <if test="userName != null"> user_name, </if>
            <if test="gender != null"> gender, </if>
            <if test="idNumber != null"> id_number, </if>
            <if test="phoneNumber != null"> phone_number, </if>
            <if test="mp1Openid != null"> mp1_openid, </if>
            <if test="mp1Status != null"> mp1_status, </if>
            <if test="mp2Openid != null"> mp2_openid, </if>
            <if test="mp2Status != null"> mp2_status, </if>
            <if test="miniapp1Openid != null"> miniapp1_openid, </if>
            <if test="miniapp2Openid != null"> miniapp2_openid, </if>
            <if test="createdAt != null"> created_at, </if>
            <if test="updatedAt != null"> updated_at, </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="unionid != null"> #{unionid}, </if>
            <if test="userName != null"> #{userName}, </if>
            <if test="gender != null"> #{gender}, </if>
            <if test="idNumber != null"> #{idNumber}, </if>
            <if test="phoneNumber != null"> #{phoneNumber}, </if>
            <if test="mp1Openid != null"> #{mp1Openid}, </if>
            <if test="mp1Status != null"> #{mp1Status}, </if>
            <if test="mp2Openid != null"> #{mp2Openid}, </if>
            <if test="mp2Status != null"> #{mp2Status}, </if>
            <if test="miniapp1Openid != null"> #{miniapp1Openid}, </if>
            <if test="miniapp2Openid != null"> #{miniapp2Openid}, </if>
            <if test="createdAt != null"> #{createdAt}, </if>
            <if test="updatedAt != null"> #{updatedAt}, </if>
        </trim>
    </insert>


    <insert id="batchInsertUserInteractions" parameterType="java.util.List">
        INSERT INTO user_mp
        (unionid, user_name, gender, id_number, phone_number, mp1_openid, mp1_status, mp2_openid, mp2_status, miniapp1_openid, miniapp2_openid, created_at, updated_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.unionid}, #{item.userName}, #{item.gender}, #{item.idNumber}, #{item.phoneNumber}, #{item.mp1Openid}, #{item.mp1Status}, #{item.mp2Openid}, #{item.mp2Status}, #{item.miniapp1Openid}, #{item.miniapp2Openid}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

    <!-- 更新用户交互信息 -->
    <update id="updateUserInteraction" parameterType="com.ruoyi.ninan.domain.UserMp">
        UPDATE user_mp
        <set>
            <if test="userName != null"> user_name = #{userName}, </if>
            <if test="gender != null"> gender = #{gender}, </if>
            <if test="idNumber != null"> id_number = #{idNumber}, </if>
            <if test="phoneNumber != null"> phone_number = #{phoneNumber}, </if>
            <if test="mp1Openid != null"> mp1_openid = #{mp1Openid}, </if>
            <if test="mp1Status != null"> mp1_status = #{mp1Status}, </if>
            <if test="mp2Openid != null"> mp2_openid = #{mp2Openid}, </if>
            <if test="mp2Status != null"> mp2_status = #{mp2Status}, </if>
            <if test="miniapp1Openid != null"> miniapp1_openid = #{miniapp1Openid}, </if>
            <if test="miniapp2Openid != null"> miniapp2_openid = #{miniapp2Openid}, </if>
            <if test="updatedAt != null"> updated_at = #{updatedAt} </if>
        </set>
        WHERE unionid = #{unionid}
    </update>


    <select id="wjdryLb" resultType="string">
        SELECT mp.mp1_openid
        FROM user_info uw LEFT JOIN user_mp mp on mp.unionid=uw.unionid
        WHERE  EXISTS (
            SELECT 1
            FROM dd_cymd dc
            WHERE dc.ddid = #{ddid}
              AND dc.user_id = uw.id
        )
          and mp.unionid is not null and mp.mp1_status=1
    </select>
</mapper>