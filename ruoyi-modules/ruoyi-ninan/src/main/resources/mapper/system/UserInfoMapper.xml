<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserInfoMapper">

    <resultMap type="UserInfo" id="UserWxaResult">
        <result property="id" column="id" />
        <result property="openid" column="openid" />
        <result property="xm" column="xm" />
        <result property="sfzh" column="sfzh" />
        <result property="xxz" column="xxz" />
        <result property="sfzzm" column="sfzzm" />
        <result property="sfzfm" column="sfzfm" />
        <result property="shzt" column="shzt" />
        <result property="xb" column="xb" />
        <result property="sjh" column="sjh" />
        <result property="sfzdz" column="sfzdz" />
        <result property="unionid" column="unionid" />
        <result property="wxaCodeUrl" column="wxa_code_url" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="sf" column="sf" />
        <result property="zsyg" column="zsyg" />
        <result property="xzz" column="xzz" />
        <result property="khh" column="khh" />
        <result property="yhh" column="yhh" />
        <result property="jn" column="jn" />
        <result property="sg" column="sg" />
        <result property="dqzt" column="dqzt" />
        <result property="smrz" column="smrz" />

        <result property="tz" column="tz" />
        <result property="sfjr" column="sfjr" />
        <result property="wysp" column="wysp" />
        <result property="qyzt" column="qyzt" />
        <result property="txz" column="txz" />
        <result property="appid" column="appid" />
        <result property="sfQy" column="sf_qy" />
        <result property="gzYe" column="gz_ye" />
    </resultMap>

    <sql id="selectUserWxaVo">
        SELECT user_info.id,user_wxa.appid,user_wxa.openid, xm, sfzh, sfzzm, sfzfm, shzt, xb, sjh, sfzdz, user_info.unionid, wxa_code_url, create_time, update_time, sf, zsyg, xzz, khh, yhh, jn, sg, CASE WHEN sf = 2 AND dqzt = 2 THEN 3 ELSE dqzt END AS dqzt, smrz, wysp, sfjr, tz,(SELECT shzt from user_company where user_company.user_id=user_info.id limit 1) as qyzt, xxz FROM user_info LEFT JOIN user_wxa  on user_wxa.unionid=user_info.unionid
    </sql>

    <select id="selectUserInfoList" parameterType="UserInfo" resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        <where>
            <if test="openid != null and openid != ''"> and user_wxa.openid = #{openid}</if>
            <if test="xm != null and xm != ''"> and xm like concat('%', #{xm}, '%')</if>
            <if test="sfzh != null and sfzh != ''"> and sfzh = #{sfzh}</if>
            <if test="xxz != null and xxz != ''"> and xxz = #{xxz}</if>
            <if test="sfzzm != null and sfzzm != ''"> and sfzzm = #{sfzzm}</if>
            <if test="sfzfm != null and sfzfm != ''"> and sfzfm = #{sfzfm}</if>
            <if test="shzt != null and shzt != ''"> and shzt = #{shzt}</if>
            <if test="xb != null and xb != ''"> and xb = #{xb}</if>
            <if test="sjh != null and sjh != ''"> and sjh = #{sjh}</if>
            <if test="sfzdz != null and sfzdz != ''"> and sfzdz = #{sfzdz}</if>
            <if test="unionid != null and unionid != ''"> and unionid = #{unionid}</if>
            <if test="wxaCodeUrl != null and wxaCodeUrl != ''"> and wxa_code_url = #{wxaCodeUrl}</if>
            <if test="sf != null and sf != ''"> and sf = #{sf}</if>
            <if test="zsyg != null and zsyg != ''"> and zsyg = #{zsyg}</if>
            <if test="xzz != null and xzz != ''"> and xzz = #{xzz}</if>
            <if test="khh != null and khh != ''"> and khh = #{khh}</if>
            <if test="yhh != null and yhh != ''"> and yhh = #{yhh}</if>
            <if test="jn != null and jn != ''"> and jn = #{jn}</if>
            <if test="sg != null and sg != ''"> and sg = #{sg}</if>
            <if test="dqzt != null and dqzt != ''"> and dqzt = #{dqzt}</if>
        </where>
    </select>

    <select id="selectUserInfoById"  resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        where user_info.id = #{id} and user_info.del_flag=0 and user_wxa.appid=#{appid}
    </select>

    <select id="selectUserInfoByYhsh"  resultType="map">
        SELECT
            xm,
            xb,
            FLOOR( DATEDIFF( CURDATE( ), STR_TO_DATE( SUBSTRING( sfzh, 7, 8 ), '%Y%m%d' ) ) / 365 ) AS nl,
            txz,
            xxz,
            sfzh,
            sfzzm,
            sfzfm,
            sfz_sj as sfzSj,
            shzt,
            (SELECT info.xm from user_qb_jl jl LEFT JOIN user_info info on info.id=jl.tjr_user_id where jl.user_id=u.id)  as tjrXm
        FROM
            user_info u
        WHERE
            id = #{userId}
    </select>

    <select id="selectUserInfoByGzhXx" parameterType="Long" resultMap="UserWxaResult">
        SELECT xm,sjh,m.mp1_openid as openid FROM user_info w LEFT JOIN user_mp m on w.unionid=m.unionid
        where w.id = #{id} and w.del_flag=0
    </select>

    <select id="selectUserInfoSfzh" resultType="int">
        select count(1) from user_info where sfzh=#{sfzh} and id!=#{userId}
    </select>


    <select id="selectUserInfoByOpenid" parameterType="string" resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        where user_wxa.openid = #{openid} and user_info.del_flag=0
    </select>

    <select id="selectUserInfoByUnionid" parameterType="string" resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        where user_wxa.unionid = #{unionid} and user_info.del_flag=0 limit 1
    </select>


    <select id="selectUserInfoByUnionidAndAppid" parameterType="string" resultMap="UserWxaResult">
        SELECT user_info.id, user_wxa.appid, user_wxa.openid, xm, sfzh, sfzzm, sfzfm, 
        user_info.shzt, xb, sjh, sfzdz, user_info.unionid, wxa_code_url, 
        create_time, update_time, sf, zsyg, xzz, khh, yhh, jn, sg, 
        CASE WHEN sf = 2 AND dqzt = 2 THEN 3 ELSE dqzt END AS dqzt, 
        smrz, wysp, sfjr, tz, xxz, uc.shzt as qyzt,
        uc.type as sf_qy,uc.gz_ye
        FROM user_info 
        LEFT JOIN user_wxa ON user_wxa.unionid = user_info.unionid
        LEFT JOIN user_company uc ON uc.user_id = user_info.id
        where user_wxa.unionid = #{unionid} and user_info.del_flag=0 and user_wxa.appid=#{appid}
        LIMIT 1
    </select>

    <select id="userExists" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM user_info
            WHERE unionid = #{openid} and del_flag=0
        );
    </select>

    <select id="userExistsUnionid" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM user_info
            WHERE unionid = #{unionid}
        );
    </select>


    <select id="selectSfxg" resultType="boolean">
        SELECT EXISTS (SELECT 1 FROM user_wxa WHERE id = #{id} AND update_time = #{updateTime})
    </select>


    <insert id="insertUserInfo" parameterType="UserInfo" useGeneratedKeys="true" keyProperty="id">
        insert into user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xm != null and xm != ''">xm,</if>
            <if test="sfzh != null and sfzh != ''">sfzh,</if>
            <if test="xxz != null">xxz,</if>
            <if test="sfzzm != null">sfzzm,</if>
            <if test="sfzfm != null">sfzfm,</if>
            <if test="shzt != null">shzt,</if>
            <if test="xb != null">xb,</if>
            <if test="sjh != null">sjh,</if>
            <if test="sfzdz != null">sfzdz,</if>
            <if test="unionid != null">unionid,</if>
            <if test="wxaCodeUrl != null">wxa_code_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sf != null">sf,</if>
            <if test="zsyg != null">zsyg,</if>
            <if test="xzz != null">xzz,</if>
            <if test="khh != null">khh,</if>
            <if test="yhh != null">yhh,</if>
            <if test="jn != null">jn,</if>
            <if test="sg != null">sg,</if>
            <if test="dqzt != null">dqzt,</if>
            <if test="smrz != null">smrz,</if>
            <if test="txz != null">txz,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xm != null and xm != ''">#{xm},</if>
            <if test="sfzh != null and sfzh != ''">#{sfzh},</if>
            <if test="xxz != null">#{xxz},</if>
            <if test="sfzzm != null">#{sfzzm},</if>
            <if test="sfzfm != null">#{sfzfm},</if>
            <if test="shzt != null">#{shzt},</if>
            <if test="xb != null">#{xb},</if>
            <if test="sjh != null">#{sjh},</if>
            <if test="sfzdz != null">#{sfzdz},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="wxaCodeUrl != null">#{wxaCodeUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sf != null">#{sf},</if>
            <if test="zsyg != null">#{zsyg},</if>
            <if test="xzz != null">#{xzz},</if>
            <if test="khh != null">#{khh},</if>
            <if test="yhh != null">#{yhh},</if>
            <if test="jn != null">#{jn},</if>
            <if test="sg != null">#{sg},</if>
            <if test="dqzt != null">#{dqzt},</if>
            <if test="smrz != null">#{smrz},</if>
            <if test="txz != null">#{txz},</if>
        </trim>
    </insert>

    <update id="updateUserInfo" parameterType="UserInfo">
        update user_info
        <set>
            <if test="xm != null">xm = #{xm},</if>
            <if test="sfzh != null">sfzh = #{sfzh},</if>
            <if test="xxz != null">xxz = #{xxz},</if>
            <if test="sfzzm != null">sfzzm = #{sfzzm},</if>
            <if test="sfzfm != null">sfzfm = #{sfzfm},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="xb != null">xb = #{xb},</if>
            <if test="sjh != null">sjh = #{sjh},</if>
            <if test="sfzdz != null">sfzdz = #{sfzdz},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="wxaCodeUrl != null">wxa_code_url = #{wxaCodeUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="zsyg != null">zsyg = #{zsyg},</if>
            <if test="xzz != null">xzz = #{xzz},</if>
            <if test="khh != null">khh = #{khh},</if>
            <if test="yhh != null">yhh = #{yhh},</if>
            <if test="jn != null">jn = #{jn},</if>
            <if test="sg != null">sg = #{sg},</if>
            <if test="dqzt != null">dqzt = #{dqzt},</if>
            <if test="smrz != null">smrz = #{smrz},</if>
            <if test="tz != null">tz = #{tz},</if>
            <if test="sfjr != null">sfjr = #{sfjr},</if>
            <if test="wysp != null">wysp = #{wysp},</if>
            <if test="txz != null">txz = #{txz},</if>
        </set>
        where id = #{id}
    </update>

    <update id="updateYhsh">
        update user_info set shzt=#{shzt} where id=#{userId}
    </update>

<!--    -->
    <update id="updateUserInfoOpenid" parameterType="UserInfo">
        update user_info
        <set>
            <if test="xm != null">xm = #{xm},</if>
            <if test="sfzh != null">sfzh = #{sfzh},</if>
            <if test="xxz != null">xxz = #{xxz},</if>
            <if test="sfzzm != null">sfzzm = #{sfzzm},</if>
            <if test="sfzfm != null">sfzfm = #{sfzfm},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="xb != null">xb = #{xb},</if>
            <if test="sjh != null">sjh = #{sjh},</if>
            <if test="sfzdz != null">sfzdz = #{sfzdz},</if>
            <if test="wxaCodeUrl != null">wxa_code_url = #{wxaCodeUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="zsyg != null">zsyg = #{zsyg},</if>
            <if test="xzz != null">xzz = #{xzz},</if>
            <if test="khh != null">khh = #{khh},</if>
            <if test="yhh != null">yhh = #{yhh},</if>
            <if test="jn != null">jn = #{jn},</if>
            <if test="sg != null">sg = #{sg},</if>
            <if test="dqzt != null">dqzt = #{dqzt},</if>
            <if test="smrz != null">smrz = #{smrz},</if>
            <if test="tz != null">tz = #{tz},</if>
            <if test="sfjr != null">sfjr = #{sfjr},</if>
            <if test="wysp != null">wysp = #{wysp},</if>
            <if test="txz != null">txz = #{txz},</if>
            <if test="sfzQfd != null">sfz_qfd = #{sfzQfd},</if>
            <if test="sfzSj != null">sfz_sj = #{sfzSj},</if>
        </set>
        where unionid = #{unionid}
    </update>

    <update id="deleteUserWxaById">
        update user_info set del_flag=2 where id=#{id}
    </update>

    <delete id="deleteUserWxa" parameterType="Long">
        delete from user_info
        where id = #{id}
    </delete>

</mapper>
