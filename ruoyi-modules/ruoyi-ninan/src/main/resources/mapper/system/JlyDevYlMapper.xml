<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.JlyDevYlMapper">
    
    <resultMap type="JlyDevYl" id="JlyDevYlResult">
        <result property="id"    column="id"    />
        <result property="file"    column="file"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectJlyDevYlVo">
        select id, file, cjsj, url from jly_dev_yl
    </sql>

    <select id="selectJlyDevYlList" parameterType="JlyDevYl" resultMap="JlyDevYlResult">
        <include refid="selectJlyDevYlVo"/>
        <where>  
            <if test="file != null  and file != ''"> and file = #{file}</if>
            <if test="cjsj != null  and cjsj != ''"> and cjsj = #{cjsj}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>
    
    <select id="selectJlyDevYlById" parameterType="Long" resultMap="JlyDevYlResult">
        <include refid="selectJlyDevYlVo"/>
        where id = #{id}
    </select>

    <select id="selectJlyDevYlFile" parameterType="String" resultMap="JlyDevYlResult">
        <include refid="selectJlyDevYlVo"/>
        where file = #{file}
    </select>
        
    <insert id="insertJlyDevYl" parameterType="JlyDevYl" useGeneratedKeys="true" keyProperty="id">
        insert into jly_dev_yl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="file != null">file,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="url != null">url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="file != null">#{file},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="url != null">#{url},</if>
         </trim>
    </insert>

    <update id="updateJlyDevYl" parameterType="JlyDevYl">
        update jly_dev_yl
        <trim prefix="SET" suffixOverrides=",">
            <if test="file != null">file = #{file},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="url != null">url = #{url},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJlyDevYlById" parameterType="Long">
        delete from jly_dev_yl where id = #{id}
    </delete>

    <delete id="deleteJlyDevYlByIds" parameterType="String">
        delete from jly_dev_yl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>