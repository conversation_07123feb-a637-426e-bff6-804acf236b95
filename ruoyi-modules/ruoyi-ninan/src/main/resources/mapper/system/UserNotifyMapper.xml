<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserNotifyMapper">
    
    <resultMap type="UserNotify" id="UserNotifyResult">
        <result property="id"    column="id"    />
        <result property="nr"    column="nr"    />
        <result property="fsrid"    column="fsrid"    />
        <result property="jsrid"    column="jsrid"    />
        <result property="ydwd"    column="ydwd"    />
        <result property="lx"    column="lx"    />
        <result property="xxlx"    column="xxlx"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectUserNotifyVo">
        SELECT
            id,
            nr,
            fsrid,
            jsrid,
            ydwd,
            lx,
            url,
            CASE
                WHEN xxlx = 0 THEN '订单消息'
                WHEN xxlx = 1 THEN '劳务费发放'
                WHEN xxlx = 2 THEN '支付提醒'
                WHEN xxlx = 3 THEN '用户消息'
                ELSE '未知'
                END
                AS xxlx,
            cjsj
        FROM
            user_notify
    </sql>

    <select id="selectUserNotifyList" parameterType="UserNotify" resultMap="UserNotifyResult">
        <include refid="selectUserNotifyVo"/>
        <where>  
            <if test="nr != null  and nr != ''"> and nr = #{nr}</if>
            <if test="jsrid != null "> and jsrid = #{jsrid}</if>
            <if test="ydwd != null  and ydwd != ''"> and ydwd = #{ydwd}</if>
            <if test="lx != null  and lx != ''"> and lx = #{lx}</if>
            <if test="lx != null  and lx != '' and lx == 0 "> or fsrid = #{fsrid}</if>
            <if test="xxlx != null  and xxlx != ''"> and xxlx = #{xxlx}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
            and del_flag = 0
        </where>
        order by cjsj desc
    </select>
    
    <select id="selectUserNotifyById" parameterType="Long" resultMap="UserNotifyResult">
        <include refid="selectUserNotifyVo"/>
        where id = #{id}
    </select>

    <select id="selectWdsl" parameterType="string" resultType="int">
        SELECT COUNT(jsrid) as wdsl FROM user_notify WHERE jsrid = #{jsrid} and ydwd=0 and del_flag=0
    </select>
        
    <insert id="insertUserNotify" parameterType="UserNotify" useGeneratedKeys="true" keyProperty="id">
        insert into user_notify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nr != null">nr,</if>
            <if test="fsrid != null">fsrid,</if>
            <if test="jsrid != null">jsrid,</if>
            <if test="ydwd != null">ydwd,</if>
            <if test="lx != null">lx,</if>
            <if test="xxlx != null">xxlx,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="url != null">url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nr != null">#{nr},</if>
            <if test="fsrid != null">#{fsrid},</if>
            <if test="jsrid != null">#{jsrid},</if>
            <if test="ydwd != null">#{ydwd},</if>
            <if test="lx != null">#{lx},</if>
            <if test="xxlx != null">#{xxlx},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="url != null">#{url},</if>
         </trim>
    </insert>

    <update id="updateUserNotify" parameterType="UserNotify">
        update user_notify
        <trim prefix="SET" suffixOverrides=",">
            <if test="nr != null">nr = #{nr},</if>
            <if test="fsrid != null">fsrid = #{fsrid},</if>
            <if test="jsrid != null">jsrid = #{jsrid},</if>
            <if test="ydwd != null">ydwd = #{ydwd},</if>
            <if test="lx != null">lx = #{lx},</if>
            <if test="xxlx != null">xxlx = #{xxlx},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatYd" parameterType="UserNotify">
        update user_notify set ydwd = 1
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="jsrid != null ">and jsrid = #{jsrid}</if>
            and ydwd = 0
        </where>
    </update>

    <delete id="deleteNotify" parameterType="UserNotify">
        update user_notify set del_flag = 2
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="jsrid != null ">and jsrid = #{jsrid}</if>
            and del_flag = 0 and lx = 1
        </where>
    </delete>

    <delete id="deleteUserNotifyById" parameterType="Long">
        delete from user_notify where id = #{id}
    </delete>

    <delete id="deleteUserNotifyByIds" parameterType="String">
        delete from user_notify where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>