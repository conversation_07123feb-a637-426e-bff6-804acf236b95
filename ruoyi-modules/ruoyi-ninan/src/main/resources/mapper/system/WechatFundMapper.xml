<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.WechatFundMapper">
    
    <resultMap type="WechatFund" id="WechatFundResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="userName"    column="user_name"    />
        <result property="type"    column="type"    />
        <result property="total"    column="total"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="outTradeNo"    column="out_trade_no"    />
        <result property="outRefundNo"    column="out_refund_no"    />
        <result property="refundId"    column="refund_id"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="refundTime"    column="refund_time"    />
        <result property="mchid"    column="mchid"    />
        <result property="appid"    column="appid"    />
        <result property="ddbh"    column="ddbh"    />
    </resultMap>

    <sql id="selectWechatFundVo">
        select id, openid, user_name, type, total, transaction_id, out_trade_no, out_refund_no, refund_id, payment_time, refund_time, ddbh
        from wechat_fund
    </sql>

    <select id="selectWechatFundList" parameterType="WechatFund" resultMap="WechatFundResult">
        <include refid="selectWechatFundVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="total != null "> and total = #{total}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="outTradeNo != null  and outTradeNo != ''"> and out_trade_no = #{outTradeNo}</if>
            <if test="outRefundNo != null  and outRefundNo != ''"> and out_refund_no = #{outRefundNo}</if>
            <if test="refundId != null  and refundId != ''"> and refund_id = #{refundId}</if>
            <if test="paymentTime != null  and paymentTime != ''"> and payment_time = #{paymentTime}</if>
            <if test="refundTime != null  and refundTime != ''"> and refund_time = #{refundTime}</if>
            <if test="mchid != null  and mchid != ''"> and mchid = #{mchid}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="ddbh != null  and ddbh != ''"> and ddbh = #{ddbh}</if>
        </where>
    </select>
    
    <select id="selectWechatFundById" parameterType="Long" resultMap="WechatFundResult">
        <include refid="selectWechatFundVo"/>
        where id = #{id}
    </select>

    <select id="selectTransactionId" parameterType="String" resultMap="WechatFundResult">
        <include refid="selectWechatFundVo"/>
        where transaction_id = #{transactionId}
    </select>

    <insert id="insertWechatFund" parameterType="WechatFund" useGeneratedKeys="true" keyProperty="id">
        insert into wechat_fund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null">openid,</if>
            <if test="userName != null">user_name,</if>
            <if test="type != null">type,</if>
            <if test="total != null">total,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="outTradeNo != null">out_trade_no,</if>
            <if test="outRefundNo != null">out_refund_no,</if>
            <if test="refundId != null">refund_id,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="mchid != null">mchid,</if>
            <if test="appid != null">appid,</if>
            <if test="ddbh != null">ddbh,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null">#{openid},</if>
            <if test="userName != null">#{userName},</if>
            <if test="type != null">#{type},</if>
            <if test="total != null">#{total},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="outTradeNo != null">#{outTradeNo},</if>
            <if test="outRefundNo != null">#{outRefundNo},</if>
            <if test="refundId != null">#{refundId},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="mchid != null">#{mchid},</if>
            <if test="appid != null">#{appid},</if>
            <if test="ddbh != null">#{ddbh},</if>
         </trim>
    </insert>

    <update id="updateWechatFund" parameterType="WechatFund">
        update wechat_fund
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null">openid = #{openid},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="total != null">total = #{total},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="outTradeNo != null">out_trade_no = #{outTradeNo},</if>
            <if test="outRefundNo != null">out_refund_no = #{outRefundNo},</if>
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="mchid != null">mchid = #{mchid},</if>
            <if test="appid != null">appid = #{appid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWechatFundById" parameterType="Long">
        delete from wechat_fund where id = #{id}
    </delete>

    <delete id="deleteWechatFundByIds" parameterType="String">
        delete from wechat_fund where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>