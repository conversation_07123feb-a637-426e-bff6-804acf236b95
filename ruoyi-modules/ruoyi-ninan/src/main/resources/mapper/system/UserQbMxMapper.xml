<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserQbMxMapper">
    
    <resultMap type="UserQbMx" id="UserQbMxResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="xgje"    column="xgje"    />
        <result property="ye"    column="ye"    />
        <result property="szlx"    column="szlx"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="bz"    column="bz"    />
        <result property="lzlx"    column="lzlx"    />
        <result property="sf"    column="sf"    />
        <result property="ddbh"    column="ddbh"    />
        <result property="endTime"    column="endTime"    />
    </resultMap>

    <sql id="selectUserQbMxVo">
        select id, user_id, xgje, ye, szlx, cjsj, bz, lzlx, sf, ddbh from user_qb_mx
    </sql>

    <select id="selectUserQbMxList" parameterType="UserQbMx" resultMap="UserQbMxResult">
        <include refid="selectUserQbMxVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="xgje != null "> and xgje = #{xgje}</if>
            <if test="ye != null "> and ye = #{ye}</if>
            <if test="szlx != null  and szlx != ''"> and szlx = #{szlx}</if>
            <if test="cjsj != null and endTime!=null"> and cjsj BETWEEN  #{cjsj}  and #{endTime} </if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
            <if test="lzlx != null  and lzlx != ''"> and lzlx = #{lzlx}</if>
            <if test="sf != null  and sf != ''"> and sf = #{sf}</if>
            <if test="ddbh != null  and ddbh != ''"> and ddbh = #{ddbh}</if>
        </where>
        order by cjsj desc
    </select>
    
    <select id="selectUserQbMxById" parameterType="Long" resultMap="UserQbMxResult">
        <include refid="selectUserQbMxVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserQbMx" parameterType="UserQbMx" useGeneratedKeys="true" keyProperty="id">
        insert into user_qb_mx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="xgje != null">xgje,</if>
            <if test="ye != null">ye,</if>
            <if test="szlx != null">szlx,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="bz != null">bz,</if>
            <if test="lzlx != null">lzlx,</if>
            <if test="sf != null">sf,</if>
            <if test="ddbh != null">ddbh,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="xgje != null">#{xgje},</if>
            <if test="ye != null">#{ye},</if>
            <if test="szlx != null">#{szlx},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="bz != null">#{bz},</if>
            <if test="lzlx != null">#{lzlx},</if>
            <if test="sf != null">#{sf},</if>
            <if test="ddbh != null">#{ddbh},</if>
         </trim>
    </insert>

    <update id="updateUserQbMx" parameterType="UserQbMx">
        update user_qb_mx
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="xgje != null">xgje = #{xgje},</if>
            <if test="ye != null">ye = #{ye},</if>
            <if test="szlx != null">szlx = #{szlx},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="lzlx != null">lzlx = #{lzlx},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="ddbh != null">ddbh = #{ddbh},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserQbMxById" parameterType="Long">
        delete from user_qb_mx where id = #{id}
    </delete>

    <delete id="deleteUserQbMxByIds" parameterType="String">
        delete from user_qb_mx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>