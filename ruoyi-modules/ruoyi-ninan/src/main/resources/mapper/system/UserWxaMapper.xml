<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserWxaMapper">
    
    <resultMap type="UserWxa" id="UserWxaResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="appid"    column="appid"    />
        <result property="openid"    column="openid"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="unionid"    column="unionid"    />
    </resultMap>

    <sql id="selectUserWxaVo">
        select id, user_id, appid, openid,unionid, cjsj from user_wxa
    </sql>

    <select id="selectUserWxaList" parameterType="UserWxa" resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
        </where>
    </select>
    
    <select id="selectUserWxaById" parameterType="Long" resultMap="UserWxaResult">
        <include refid="selectUserWxaVo"/>
        where id = #{id}
    </select>

    <select id="findUserWxaByOpenid" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM user_wxa
            WHERE openid = #{openid} and del_flag=0
        )
    </select>

    <select id="selectUserWxaConfigByAppid" resultType="string">
        select app_secret from user_wxa_config where appid=#{appid}
    </select>
        
    <insert id="insertUserWxa" parameterType="UserWxa" useGeneratedKeys="true" keyProperty="id">
        insert into user_wxa
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="appid != null and appid != ''">appid,</if>
            <if test="openid != null">openid,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="unionid != null">unionid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="appid != null and appid != ''">#{appid},</if>
            <if test="openid != null">#{openid},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="unionid != null">#{unionid},</if>
         </trim>
    </insert>

    <update id="updateUserWxa" parameterType="UserWxa">
        update user_wxa
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="appid != null and appid != ''">appid = #{appid},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
        </trim>
        where openid = #{openid}
    </update>

    <delete id="deleteUserWxaById" parameterType="Long">
        delete from user_wxa where id = #{id}
    </delete>

    <delete id="deleteUserWxaByIds" parameterType="String">
        delete from user_wxa where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>