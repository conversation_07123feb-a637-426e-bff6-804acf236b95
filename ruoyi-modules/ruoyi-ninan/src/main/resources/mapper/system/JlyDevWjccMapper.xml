<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.JlyDevWjccMapper">
    
    <resultMap type="JlyDevWjcc" id="JlyDevWjccResult">
        <result property="id"    column="id"    />
        <result property="beg"    column="beg"    />
        <result property="end"    column="end"    />
        <result property="rq"    column="rq"    />
        <result property="wjmc"    column="wjmc"    />
        <result property="sbid"    column="sbid"    />
        <result property="len"    column="len"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="file"    column="file"    />
    </resultMap>

    <sql id="selectJlyDevWjccVo">
        select id, beg, end, rq, wjmc, sbid, len, cjsj, file from jly_dev_wjcc
    </sql>

    <select id="selectJlyDevWjccList" parameterType="JlyDevWjcc" resultMap="JlyDevWjccResult">
        <include refid="selectJlyDevWjccVo"/>
        <where>  
            <if test="beg != null  and beg != ''"> and beg = #{beg}</if>
            <if test="end != null  and end != ''"> and end = #{end}</if>
            <if test="rq != null  and rq != ''"> and rq = #{rq}</if>
            <if test="wjmc != null  and wjmc != ''"> and wjmc = #{wjmc}</if>
            <if test="sbid != null  and sbid != ''"> and sbid = #{sbid}</if>
            <if test="len != null "> and len = #{len}</if>
            <if test="cjsj != null  and cjsj != ''"> and cjsj = #{cjsj}</if>
            <if test="file != null  and file != ''"> and file = #{file}</if>
        </where>
    </select>
    
    <select id="selectJlyDevWjccById" parameterType="Long" resultMap="JlyDevWjccResult">
        <include refid="selectJlyDevWjccVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertJlyDevWjcc" parameterType="JlyDevWjcc" useGeneratedKeys="true" keyProperty="id">
        insert into jly_dev_wjcc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="beg != null">beg,</if>
            <if test="end != null">end,</if>
            <if test="rq != null">rq,</if>
            <if test="wjmc != null">wjmc,</if>
            <if test="sbid != null">sbid,</if>
            <if test="len != null">len,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="file != null">file,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="beg != null">#{beg},</if>
            <if test="end != null">#{end},</if>
            <if test="rq != null">#{rq},</if>
            <if test="wjmc != null">#{wjmc},</if>
            <if test="sbid != null">#{sbid},</if>
            <if test="len != null">#{len},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="file != null">#{file},</if>
         </trim>
    </insert>

    <update id="updateJlyDevWjcc" parameterType="JlyDevWjcc">
        update jly_dev_wjcc
        <trim prefix="SET" suffixOverrides=",">
            <if test="beg != null">beg = #{beg},</if>
            <if test="end != null">end = #{end},</if>
            <if test="rq != null">rq = #{rq},</if>
            <if test="wjmc != null">wjmc = #{wjmc},</if>
            <if test="sbid != null">sbid = #{sbid},</if>
            <if test="len != null">len = #{len},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="file != null">file = #{file},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJlyDevWjccById" parameterType="Long">
        delete from jly_dev_wjcc where id = #{id}
    </delete>

    <delete id="deleteJlyDevWjccByIds" parameterType="String">
        delete from jly_dev_wjcc where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>