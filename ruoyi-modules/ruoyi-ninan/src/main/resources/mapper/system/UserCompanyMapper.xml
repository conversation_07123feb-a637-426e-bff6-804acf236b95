<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserCompanyMapper">
    
    <resultMap type="UserCompany" id="UserCompanyResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="qymc"    column="qymc"    />
        <result property="qydm"    column="qydm"    />
        <result property="qyzh"    column="qyzh"    />
        <result property="hylb"    column="hylb"    />
        <result property="fzrMc"    column="fzr_mc"    />
        <result property="fzrDh"    column="fzr_dh"    />
        <result property="dzyx"    column="dzyx"    />
        <result property="address"    column="address"    />
        <result property="capital"    column="capital"    />
        <result property="type"    column="type"    />
        <result property="shzt"    column="shzt"    />
        <result property="yx"    column="yx"    />
        <result property="yyzzdz"    column="yyzzdz"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="xgsj"    column="xgsj"    />
        <result property="gzYe"    column="gz_ye"    />
    </resultMap>

    <sql id="selectUserCompanyVo">
        select id, user_id, qymc, qydm, qyzh, hylb, fzr_mc, fzr_dh, dzyx, address, capital, type, shzt, yx, yyzzdz, cjsj, xgsj, gz_ye from user_company
    </sql>

    <select id="selectUserCompanyList" parameterType="UserCompany" resultMap="UserCompanyResult">
        <include refid="selectUserCompanyVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="qymc != null  and qymc != ''"> and qymc = #{qymc}</if>
            <if test="qydm != null  and qydm != ''"> and qydm = #{qydm}</if>
            <if test="qyzh != null  and qyzh != ''"> and qyzh = #{qyzh}</if>
            <if test="hylb != null  and hylb != ''"> and hylb = #{hylb}</if>
            <if test="fzrMc != null  and fzrMc != ''"> and fzr_mc = #{fzrMc}</if>
            <if test="fzrDh != null  and fzrDh != ''"> and fzr_dh = #{fzrDh}</if>
            <if test="dzyx != null  and dzyx != ''"> and dzyx = #{dzyx}</if>
            <if test="capital != null  and capital != ''"> and capital = #{capital}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="shzt != null "> and shzt = #{shzt}</if>
            <if test="yx != null  and yx != ''"> and yx = #{yx}</if>
            <if test="yyzzdz != null  and yyzzdz != ''"> and yyzzdz = #{yyzzdz}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
            <if test="xgsj != null "> and xgsj = #{xgsj}</if>
        </where>
    </select>

        <select id="selectUserCompanyByUserId" parameterType="Long" resultMap="UserCompanyResult">
            <include refid="selectUserCompanyVo"/>
            where user_id = #{userId} and del_flag=0
        </select>

        <select id="selectUserCompanyByUserShzt" parameterType="Long" resultMap="UserCompanyResult">
            select shzt,qymc,qydm,type from user_company
            where user_id = #{userId} and del_flag=0
        </select>
        
        <select id="selectUserCompanyByUserIdAndType" resultMap="UserCompanyResult">
            <include refid="selectUserCompanyVo"/>
            where user_id = #{userId} and type = #{type} and del_flag=0
        </select>

    
    <select id="selectUserCompanyById" parameterType="Long" resultMap="UserCompanyResult">
        <include refid="selectUserCompanyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserCompany" parameterType="UserCompany" useGeneratedKeys="true" keyProperty="id">
        insert into user_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="qymc != null">qymc,</if>
            <if test="qydm != null">qydm,</if>
            <if test="qyzh != null">qyzh,</if>
            <if test="hylb != null">hylb,</if>
            <if test="fzrMc != null">fzr_mc,</if>
            <if test="fzrDh != null">fzr_dh,</if>
            <if test="dzyx != null">dzyx,</if>
            <if test="address != null">address,</if>
            <if test="capital != null">capital,</if>
            <if test="type != null">type,</if>
            <if test="shzt != null">shzt,</if>
            <if test="yx != null">yx,</if>
            <if test="yyzzdz != null">yyzzdz,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="xgsj != null">xgsj,</if>
            <if test="gzYe != null">gz_ye,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="qymc != null">#{qymc},</if>
            <if test="qydm != null">#{qydm},</if>
            <if test="qyzh != null">#{qyzh},</if>
            <if test="hylb != null">#{hylb},</if>
            <if test="fzrMc != null">#{fzrMc},</if>
            <if test="fzrDh != null">#{fzrDh},</if>
            <if test="dzyx != null">#{dzyx},</if>
            <if test="address != null">#{address},</if>
            <if test="capital != null">#{capital},</if>
            <if test="type != null">#{type},</if>
            <if test="shzt != null">#{shzt},</if>
            <if test="yx != null">#{yx},</if>
            <if test="yyzzdz != null">#{yyzzdz},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="xgsj != null">#{xgsj},</if>
            <if test="gzYe != null">#{gzYe},</if>
         </trim>
    </insert>

    <update id="updateUserCompany" parameterType="UserCompany">
        update user_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="qymc != null">qymc = #{qymc},</if>
            <if test="qydm != null">qydm = #{qydm},</if>
            <if test="qyzh != null">qyzh = #{qyzh},</if>
            <if test="hylb != null">hylb = #{hylb},</if>
            <if test="fzrMc != null">fzr_mc = #{fzrMc},</if>
            <if test="fzrDh != null">fzr_dh = #{fzrDh},</if>
            <if test="dzyx != null">dzyx = #{dzyx},</if>
            <if test="address != null">address = #{address},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="type != null">type = #{type},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="yx != null">yx = #{yx},</if>
            <if test="yyzzdz != null">yyzzdz = #{yyzzdz},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="xgsj != null">xgsj = #{xgsj},</if>
            <if test="gzYe != null">gz_ye = #{gzYe},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateUserCompanyUserId" parameterType="UserCompany">
        update user_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="qymc != null">qymc = #{qymc},</if>
            <if test="qydm != null">qydm = #{qydm},</if>
            <if test="qyzh != null">qyzh = #{qyzh},</if>
            <if test="hylb != null">hylb = #{hylb},</if>
            <if test="fzrMc != null">fzr_mc = #{fzrMc},</if>
            <if test="fzrDh != null">fzr_dh = #{fzrDh},</if>
            <if test="dzyx != null">dzyx = #{dzyx},</if>
            <if test="address != null">address = #{address},</if>
            <if test="capital != null">capital = #{capital},</if>
            <if test="type != null">type = #{type},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="yx != null">yx = #{yx},</if>
            <if test="yyzzdz != null">yyzzdz = #{yyzzdz},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="xgsj != null">xgsj = #{xgsj},</if>
            <if test="gzYe != null">gz_ye = #{gzYe},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <update id="updateUserBalance">
        update user_company
        set gz_ye = #{gzYe}
        where user_id = #{userId}
    </update>

    <delete id="deleteUserCompanyById" parameterType="Long">
        delete from user_company where id = #{id}
    </delete>

    <delete id="deleteUserCompanyByIds" parameterType="String">
        delete from user_company where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>