<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.SysSmsLogMapper">
    
    <resultMap type="SysSmsLog" id="SysSmsLogResult">
        <result property="id"    column="id"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="signName"    column="sign_name"    />
        <result property="templateCode"    column="template_code"    />
        <result property="templateParam"    column="template_param"    />
        <result property="sendStatus"    column="send_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSysSmsLogVo">
        select id, phone_number, sign_name, template_code, template_param, send_status, error_message, create_time from sys_sms_log
    </sql>

    <select id="selectSysSmsLogList" parameterType="SysSmsLog" resultMap="SysSmsLogResult">
        <include refid="selectSysSmsLogVo"/>
        <where>  
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="signName != null  and signName != ''"> and sign_name like concat('%', #{signName}, '%')</if>
            <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode}</if>
            <if test="templateParam != null  and templateParam != ''"> and template_param = #{templateParam}</if>
            <if test="sendStatus != null  and sendStatus != ''"> and send_status = #{sendStatus}</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
        </where>
    </select>
    
    <select id="selectSysSmsLogById" parameterType="Long" resultMap="SysSmsLogResult">
        <include refid="selectSysSmsLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysSmsLog" parameterType="SysSmsLog" useGeneratedKeys="true" keyProperty="id">
        insert into sys_sms_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="signName != null">sign_name,</if>
            <if test="templateCode != null">template_code,</if>
            <if test="templateParam != null">template_param,</if>
            <if test="sendStatus != null">send_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="signName != null">#{signName},</if>
            <if test="templateCode != null">#{templateCode},</if>
            <if test="templateParam != null">#{templateParam},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSysSmsLog" parameterType="SysSmsLog">
        update sys_sms_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="signName != null">sign_name = #{signName},</if>
            <if test="templateCode != null">template_code = #{templateCode},</if>
            <if test="templateParam != null">template_param = #{templateParam},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysSmsLogById" parameterType="Long">
        delete from sys_sms_log where id = #{id}
    </delete>

    <delete id="deleteSysSmsLogByIds" parameterType="String">
        delete from sys_sms_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>