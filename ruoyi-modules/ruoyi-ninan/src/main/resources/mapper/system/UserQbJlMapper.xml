<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserQbJlMapper">
    
    <resultMap type="UserQbJl" id="UserQbJlResult">
        <result property="id"    column="id"    />
        <result property="tjrUserId"    column="tjr_user_id"    />
        <result property="userId"    column="user_id"    />
        <result property="jlFfZt"    column="jl_ff_zt"    />
        <result property="jlJe"    column="jl_je"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="ddid"    column="ddid"    />
    </resultMap>

    <sql id="selectUserQbJlVo">
        select id, tjr_user_id, user_id, jl_ff_zt, jl_je, cjsj, ddid from user_qb_jl
    </sql>

    <select id="selectUserQbJlList" parameterType="UserQbJl" resultMap="UserQbJlResult">
        <include refid="selectUserQbJlVo"/>
        <where>  
            <if test="tjrUserId != null "> and tjr_user_id = #{tjrUserId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="jlFfZt != null  and jlFfZt != ''"> and jl_ff_zt = #{jlFfZt}</if>
            <if test="jlJe != null  and jlJe != ''"> and jl_je = #{jlJe}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
            <if test="ddid != null "> and ddid = #{ddid}</if>
        </where>
    </select>

    <select id="selectTjrsJlje">
        SELECT
            COALESCE(SUM(CASE WHEN jl.jl_ff_zt = 0 THEN jl.jl_je ELSE 0 END), 0) AS cgJe,
            COALESCE(SUM(CASE WHEN jl.jl_ff_zt = 1 THEN jl.jl_je ELSE 0 END), 0) AS wffJe,
            COUNT(jl.jl_ff_zt) AS ytjRs
        FROM
            user_qb_jl jl
        WHERE
            jl.tjr_user_id = #{userId}
          AND jl.jl_ff_zt IN (0, 1)
    </select>

    <select id="selectJlList">
        SELECT
            info.xm,
            DATE_FORMAT(info.create_time, '%Y-%m-%d %H:%i:%s') AS jlsj,
            jl.jl_ff_zt AS jlzt
        FROM
            user_qb_jl jl
                LEFT JOIN
            user_info info
            ON jl.user_id = info.id
        WHERE
            jl.tjr_user_id = #{userId}
    </select>
    
    <select id="selectUserQbJlById" parameterType="Long" resultMap="UserQbJlResult">
        <include refid="selectUserQbJlVo"/>
        where id = #{id}
    </select>

    <select id="selectUserQbJlByUserId" parameterType="Long" resultMap="UserQbJlResult">
        select id, tjr_user_id, user_id, jl_ff_zt, jl_je from user_qb_jl
        where user_id = #{id} and jl_ff_zt=1
    </select>


    <select id="selectUserQbJlByUserXx" parameterType="UserQbJl" resultMap="UserQbJlResult">
        select id, tjr_user_id, user_id, jl_ff_zt, jl_je from user_qb_jl
        where jl_ff_zt=0
        <if test="tjrUserId != null "> and tjr_user_id = #{tjrUserId}</if>
        <if test="userId != null "> and user_id = #{userId}</if>
        <if test="ddid != null "> and ddid = #{ddid}</if>
    </select>

        
    <insert id="insertUserQbJl" parameterType="UserQbJl" useGeneratedKeys="true" keyProperty="id">
        insert into user_qb_jl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tjrUserId != null">tjr_user_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="jlFfZt != null">jl_ff_zt,</if>
            <if test="jlJe != null">jl_je,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tjrUserId != null">#{tjrUserId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="jlFfZt != null">#{jlFfZt},</if>
            <if test="jlJe != null">#{jlJe},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateUserQbJl" parameterType="UserQbJl">
        update user_qb_jl
        <trim prefix="SET" suffixOverrides=",">
            <if test="tjrUserId != null">tjr_user_id = #{tjrUserId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="jlFfZt != null">jl_ff_zt = #{jlFfZt},</if>
            <if test="jlJe != null">jl_je = #{jlJe},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="ddid != null">ddid = #{ddid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserQbJlById" parameterType="Long">
        delete from user_qb_jl where id = #{id}
    </delete>

    <delete id="deleteUserQbJlByIds" parameterType="String">
        delete from user_qb_jl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>