<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.EmployeeSalaryMapper">
    
    <resultMap type="EmployeeSalary" id="EmployeeSalaryResult">
        <result property="id"    column="id"    />
        <result property="ddId"    column="dd_id"    />
        <result property="userName"    column="user_name"    />
        <result property="openid"    column="openid"    />
        <result property="sfgz"    column="sfgz"    />
        <result property="jj1"    column="jj1"    />
        <result property="jj2"    column="jj2"    />
        <result property="vipAward"    column="vip_award"    />
        <result property="bt"    column="bt"    />
        <result property="wjkk"    column="wjkk"    />
        <result property="sfhj"    column="sfhj"    />
        <result property="bz"    column="bz"    />
        <result property="dj"    column="dj"    />
        <result property="bs"    column="bs"    />
        <result property="zgs"    column="zgs"    />
        <result property="sfzh"    column="sfzh"    />
        <result property="phone"    column="phone"    />
        <result property="gzffsj"    column="gzffsj"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="zjlshsj"    column="zjlshsj"    />
        <result property="xh"    column="xh"    />
        <result property="gzffzt"    column="gzffzt"    />
        <result property="zjlsh"    column="zjlsh"    />
    </resultMap>

    <sql id="selectEmployeeSalaryVo">
        select id, dd_id, user_name, openid, sfgz, jj1, jj2, vip_award, bt, wjkk, sfhj, bz, dj, bs, zgs, sfzh, phone, gzffsj, xmmc, zjlshsj, xh, gzffzt, zjlsh from employee_salary
    </sql>

    <select id="selectEmployeeSalaryList" parameterType="EmployeeSalary" resultMap="EmployeeSalaryResult">
        <include refid="selectEmployeeSalaryVo"/>
        <where>
            <if test="ddId != null  and ddId != ''"> and dd_id = #{ddId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="sfgz != null "> and sfgz = #{sfgz}</if>
            <if test="jj1 != null "> and jj1 = #{jj1}</if>
            <if test="jj2 != null "> and jj2 = #{jj2}</if>
            <if test="vipAward != null "> and vip_award = #{vipAward}</if>
            <if test="bt != null "> and bt = #{bt}</if>
            <if test="wjkk != null "> and wjkk = #{wjkk}</if>
            <if test="sfhj != null "> and sfhj = #{sfhj}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
            <if test="dj != null "> and dj = #{dj}</if>
            <if test="bs != null "> and bs = #{bs}</if>
            <if test="zgs != null "> and zgs = #{zgs}</if>
            <if test="sfzh != null  and sfzh != ''"> and sfzh = #{sfzh}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="gzffsj != null  and gzffsj != ''"> and gzffsj = #{gzffsj}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="zjlshsj != null  and zjlshsj != ''"> and zjlshsj = #{zjlshsj}</if>
            <if test="xh != null  and xh != ''"> and xh = #{xh}</if>
            <if test="gzffzt != null "> and gzffzt = #{gzffzt}</if>
            <if test="zjlsh != null  and zjlsh != ''"> and zjlsh = #{zjlsh}</if>
        </where>
    </select>



    <select id="selectBbxx" resultType="EmployeeSalary">
        SELECT
            ANY_VALUE(c.sbdksj) AS sbdksj,
            ANY_VALUE(c.xbdksj) AS xbdksj,
            SUM(c.rx) AS 'sfgz',
                u.xm AS 'userName',
                MAX(SUBSTRING(u.sjh,1,11)) AS phone,
            MAX(u.sfzh) AS sfzh,
            COUNT(*) bs,
            ANY_VALUE(d.rwmc) AS xmmc,
            ANY_VALUE(d.gzffsj) AS gzffsj,
            ANY_VALUE(d.ddbh) AS ddId,
            u.id AS userId,
            ROUND(SUM(TIMESTAMPDIFF(MINUTE, c.sbdksj, c.xbdksj) / 60), 1) AS zgs
        FROM dd_cymd c
                 LEFT JOIN user_info u ON c.user_id = u.id
                 LEFT JOIN dd d ON c.ddid = d.id
        WHERE c.ddid = #{ddid}
          AND c.rx > 0
          AND c.del_flag = 0
        GROUP BY u.xm, u.id;
    </select>

    <select id="selectEmployeeSalaryById" parameterType="Long" resultMap="EmployeeSalaryResult">
        <include refid="selectEmployeeSalaryVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployeeSalary" parameterType="EmployeeSalary" useGeneratedKeys="true" keyProperty="id">
        insert into employee_salary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ddId != null">dd_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="openid != null">openid,</if>
            <if test="sfgz != null">sfgz,</if>
            <if test="jj1 != null">jj1,</if>
            <if test="jj2 != null">jj2,</if>
            <if test="vipAward != null">vip_award,</if>
            <if test="bt != null">bt,</if>
            <if test="wjkk != null">wjkk,</if>
            <if test="sfhj != null">sfhj,</if>
            <if test="bz != null">bz,</if>
            <if test="dj != null">dj,</if>
            <if test="bs != null">bs,</if>
            <if test="zgs != null">zgs,</if>
            <if test="sfzh != null">sfzh,</if>
            <if test="phone != null">phone,</if>
            <if test="gzffsj != null">gzffsj,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="zjlshsj != null">zjlshsj,</if>
            <if test="xh != null">xh,</if>
            <if test="gzffzt != null">gzffzt,</if>
            <if test="zjlsh != null">zjlsh,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ddId != null">#{ddId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="openid != null">#{openid},</if>
            <if test="sfgz != null">#{sfgz},</if>
            <if test="jj1 != null">#{jj1},</if>
            <if test="jj2 != null">#{jj2},</if>
            <if test="vipAward != null">#{vipAward},</if>
            <if test="bt != null">#{bt},</if>
            <if test="wjkk != null">#{wjkk},</if>
            <if test="sfhj != null">#{sfhj},</if>
            <if test="bz != null">#{bz},</if>
            <if test="dj != null">#{dj},</if>
            <if test="bs != null">#{bs},</if>
            <if test="zgs != null">#{zgs},</if>
            <if test="sfzh != null">#{sfzh},</if>
            <if test="phone != null">#{phone},</if>
            <if test="gzffsj != null">#{gzffsj},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="zjlshsj != null">#{zjlshsj},</if>
            <if test="xh != null">#{xh},</if>
            <if test="gzffzt != null">#{gzffzt},</if>
            <if test="zjlsh != null">#{zjlsh},</if>
         </trim>
    </insert>

    <insert id="insertEmployeeSalaryBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into employee_salary (
        dd_id,
        user_name,
        openid,
        sfgz,
        sfhj,
        bz,
        dj,
        bs,
        zgs,
        sfzh,
        phone,
        gzffsj,
        xmmc,
        zjlshsj,
        xh,
        jj1,
        jj2
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.ddId},
                #{item.userName},
                #{item.openid},
                #{item.sfgz},
                #{item.sfhj},
                #{item.bz},
                #{item.dj},
                #{item.bs},
                #{item.zgs},
                #{item.sfzh},
                #{item.phone},
                #{item.gzffsj},
                #{item.xmmc},
                #{item.zjlshsj},
                #{item.xh},
                #{item.jj1},
                #{item.jj2}
            </trim>
        </foreach>
    </insert>

    <update id="updateEmployeeSalary" parameterType="EmployeeSalary">
        update employee_salary
        <trim prefix="SET" suffixOverrides=",">
            <if test="ddId != null">dd_id = #{ddId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="sfgz != null">sfgz = #{sfgz},</if>
            <if test="jj1 != null">jj1 = #{jj1},</if>
            <if test="jj2 != null">jj2 = #{jj2},</if>
            <if test="vipAward != null">vip_award = #{vipAward},</if>
            <if test="bt != null">bt = #{bt},</if>
            <if test="wjkk != null">wjkk = #{wjkk},</if>
            <if test="sfhj != null">sfhj = #{sfhj},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="dj != null">dj = #{dj},</if>
            <if test="bs != null">bs = #{bs},</if>
            <if test="zgs != null">zgs = #{zgs},</if>
            <if test="sfzh != null">sfzh = #{sfzh},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="gzffsj != null">gzffsj = #{gzffsj},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="zjlshsj != null">zjlshsj = #{zjlshsj},</if>
            <if test="xh != null">xh = #{xh},</if>
            <if test="gzffzt != null">gzffzt = #{gzffzt},</if>
            <if test="zjlsh != null">zjlsh = #{zjlsh},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeSalaryById" parameterType="Long">
        delete from employee_salary where id = #{id}
    </delete>

    <delete id="deleteEmployeeSalaryByIds" parameterType="String">
        delete from employee_salary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>