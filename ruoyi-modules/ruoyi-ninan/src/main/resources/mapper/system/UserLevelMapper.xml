<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserLevelMapper">
    
    <resultMap type="UserLevel" id="UserLevelResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="orderNum"    column="order_num"    />
        <result property="phone"    column="phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="userId"    column="user_id"    />
        <result property="tjrId"    column="tjr_id"    />
        <result property="myname"    column="myname"    />
        <result property="money"    column="money"    />
        <result property="onemoney"    column="onemoney"    />
        <result property="twomoney"    column="twomoney"    />
        <result property="threemoney"    column="threemoney"    />
    </resultMap>

    <sql id="selectUserLevelVo">
        select id, parent_id, ancestors, order_num, phone, create_time, user_id, tjr_id, myname, money, onemoney, twomoney, threemoney from user_level
    </sql>

    <select id="selectUserLevelList" parameterType="UserLevel" resultMap="UserLevelResult">
        <include refid="selectUserLevelVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="tjrId != null "> and tjr_id = #{tjrId}</if>
            <if test="myname != null  and myname != ''"> and myname like concat('%', #{myname}, '%')</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="onemoney != null "> and onemoney = #{onemoney}</if>
            <if test="twomoney != null "> and twomoney = #{twomoney}</if>
            <if test="threemoney != null "> and threemoney = #{threemoney}</if>
        </where>
    </select>
    
    <select id="selectUserLevelById" parameterType="Long" resultMap="UserLevelResult">
        <include refid="selectUserLevelVo"/>
        where id = #{id}
    </select>

    <select id="selectUserLevelByUserId" parameterType="Long" resultMap="UserLevelResult">
        <include refid="selectUserLevelVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertUserLevel" parameterType="UserLevel" useGeneratedKeys="true" keyProperty="id">
        insert into user_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="phone != null">phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="tjrId != null">tjr_id,</if>
            <if test="myname != null">myname,</if>
            <if test="money != null">money,</if>
            <if test="onemoney != null">onemoney,</if>
            <if test="twomoney != null">twomoney,</if>
            <if test="threemoney != null">threemoney,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="tjrId != null">#{tjrId},</if>
            <if test="myname != null">#{myname},</if>
            <if test="money != null">#{money},</if>
            <if test="onemoney != null">#{onemoney},</if>
            <if test="twomoney != null">#{twomoney},</if>
            <if test="threemoney != null">#{threemoney},</if>
         </trim>
    </insert>

    <update id="updateUserLevel" parameterType="UserLevel">
        update user_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="tjrId != null">tjr_id = #{tjrId},</if>
            <if test="myname != null">myname = #{myname},</if>
            <if test="money != null">money = #{money},</if>
            <if test="onemoney != null">onemoney = #{onemoney},</if>
            <if test="twomoney != null">twomoney = #{twomoney},</if>
            <if test="threemoney != null">threemoney = #{threemoney},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserLevelById" parameterType="Long">
        delete from user_level where id = #{id}
    </delete>

    <delete id="deleteUserLevelByIds" parameterType="String">
        delete from user_level where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>