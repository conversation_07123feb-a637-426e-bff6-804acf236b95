<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserQbMapper">
    
    <resultMap type="UserQb" id="UserQbResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="ye"    column="ye"    />
        <result property="hbye"    column="hbye"    />
        <result property="txzt"    column="txzt"    />
        <result property="bz"    column="bz"    />
        <result property="szlx"    column="szlx"    />
        <result property="xgje"    column="xgje"    />
        <result property="lzlx"    column="lzlx"    />
        <result property="sf"    column="sf"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="xgsj"    column="xgsj"    />
        <result property="ddbh"    column="ddbh"    />
    </resultMap>

    <sql id="selectUserQbVo">
        select id, user_id, ye, hbye, txzt, bz, szlx, xgje, lzlx, sf, cjsj, xgsj, ddbh from user_qb
    </sql>

    <select id="selectUserQbList" parameterType="UserQb" resultMap="UserQbResult">
        <include refid="selectUserQbVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="ye != null "> and ye = #{ye}</if>
            <if test="hbye != null "> and hbye = #{hbye}</if>
            <if test="txzt != null  and txzt != ''"> and txzt = #{txzt}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
            <if test="szlx != null  and szlx != ''"> and szlx = #{szlx}</if>
            <if test="xgje != null "> and xgje = #{xgje}</if>
            <if test="lzlx != null  and lzlx != ''"> and lzlx = #{lzlx}</if>
            <if test="sf != null  and sf != ''"> and sf = #{sf}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
            <if test="xgsj != null "> and xgsj = #{xgsj}</if>
            <if test="ddbh != null  and ddbh != ''"> and ddbh = #{ddbh}</if>
        </where>
    </select>
    
    <select id="selectUserQbById" parameterType="Long" resultMap="UserQbResult">
        <include refid="selectUserQbVo"/>
        where id = #{id}
    </select>

    <select id="selectUserQbByUserId" parameterType="Long" resultMap="UserQbResult">
        <include refid="selectUserQbVo"/>
        where user_id = #{userId}
    </select>


    <update id="applyPublicAccount">
        UPDATE user_nh_kc AS main
            JOIN (
            SELECT id
            FROM user_nh_kc
            WHERE user_id IS NULL
            ORDER BY RAND()
            LIMIT 1
            ) AS random_row ON main.id = random_row.id
            SET main.user_id = #{userId}
    </update>

    <select id="countUserPublicAccount" parameterType="Long" resultType="Integer">
        SELECT COUNT(1) FROM user_nh_kc WHERE user_id = #{userId}
    </select>

    <select id="getKcInfo" parameterType="Long" resultType="java.util.Map">
        SELECT 
            k.nh_kh AS cardNo,
            IFNULL(c.gz_ye, 0) AS balance
        FROM 
            user_nh_kc k 
        LEFT JOIN 
            user_company c ON k.user_id = c.user_id
        WHERE 
            k.user_id = #{userId}
    </select>

    <select id="selectJyjlList" parameterType="Long" resultType="com.ruoyi.ninan.domain.UserNhJyjl">
        SELECT 
            id,
            jy_bh AS jyBh,
            jy_je AS jyJe,
            jy_dd AS jyDd,
            jy_lx AS jyLx,
            user_id AS userId,
            cjsj,
            dq_ye as dqYe
        FROM 
            user_nh_jyjl
        WHERE 
            user_id = #{userId}
        ORDER BY
            cjsj DESC
    </select>

    <select id="selectUserQbByYhye" parameterType="Long" resultType="BigDecimal">
        select ye from user_qb where user_id=#{userId} limit 1
    </select>

    <select id="getBalance" parameterType="Long" resultType="BigDecimal">
        select ye from user_qb
        where user_id = #{userId}
    </select>

    <select id="getBalanceFro" parameterType="Long" resultType="BigDecimal">
        select ye from user_qb
        where user_id = #{userId} FOR UPDATE
    </select>
        
    <insert id="insertUserQb" parameterType="UserQb" useGeneratedKeys="true" keyProperty="id">
        insert into user_qb
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="ye != null">ye,</if>
            <if test="hbye != null">hbye,</if>
            <if test="txzt != null">txzt,</if>
            <if test="bz != null">bz,</if>
            <if test="szlx != null">szlx,</if>
            <if test="xgje != null">xgje,</if>
            <if test="lzlx != null">lzlx,</if>
            <if test="sf != null">sf,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="xgsj != null">xgsj,</if>
            <if test="ddbh != null">ddbh,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="ye != null">#{ye},</if>
            <if test="hbye != null">#{hbye},</if>
            <if test="txzt != null">#{txzt},</if>
            <if test="bz != null">#{bz},</if>
            <if test="szlx != null">#{szlx},</if>
            <if test="xgje != null">#{xgje},</if>
            <if test="lzlx != null">#{lzlx},</if>
            <if test="sf != null">#{sf},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="xgsj != null">#{xgsj},</if>
            <if test="ddbh != null">#{ddbh},</if>
         </trim>
    </insert>

    <update id="updateUserQb" parameterType="UserQb">
        update user_qb
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="ye != null">ye = #{ye},</if>
            <if test="hbye != null">hbye = #{hbye},</if>
            <if test="txzt != null">txzt = #{txzt},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="szlx != null">szlx = #{szlx},</if>
            <if test="xgje != null">xgje = #{xgje},</if>
            <if test="lzlx != null">lzlx = #{lzlx},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="xgsj != null">xgsj = #{xgsj},</if>
            <if test="ddbh != null">ddbh = #{ddbh},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateUserQbUserId" parameterType="UserQb">
        update user_qb
        <trim prefix="SET" suffixOverrides=",">
            <if test="ye != null">ye = #{ye},</if>
            <if test="hbye != null">hbye = hbye+#{hbye},</if>
            <if test="txzt != null">txzt = #{txzt},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="szlx != null">szlx = #{szlx},</if>
            <if test="xgje != null">xgje = #{xgje},</if>
            <if test="lzlx != null">lzlx = #{lzlx},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="xgsj != null">xgsj = #{xgsj},</if>
            <if test="ddbh != null">ddbh = #{ddbh},</if>
        </trim>
        where user_id = #{userId}
    </update>



    <update id="deleteUserQbById" parameterType="Long">
        update user_qb set del_flag=2 where id = #{id}
    </update>

    <delete id="deleteUserQbByIds" parameterType="String">
        update user_qb set del_flag=2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>