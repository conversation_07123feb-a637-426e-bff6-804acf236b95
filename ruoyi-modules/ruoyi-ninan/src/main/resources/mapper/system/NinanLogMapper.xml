<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.NinanLogMapper">

    <insert id="insertOperationLog" parameterType="com.ruoyi.ninan.utils.log.OperationLogEntity">
        INSERT INTO sys_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="businessType != null">business_type,</if>
            <if test="operatorType != null">operator_type,</if>
            <if test="methodName != null">method_name,</if>
            <if test="requestData != null">request_data,</if>
            <if test="responseData != null">response_data,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="requestUrl != null">request_url,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="operator != null">operator,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="operatorType != null">#{operatorType},</if>
            <if test="methodName != null">#{methodName},</if>
            <if test="requestData != null">#{requestData},</if>
            <if test="responseData != null">#{responseData},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="requestUrl != null">#{requestUrl},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="operator != null">#{operator},</if>
        </trim>
    </insert>
	 

    
</mapper>