<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserQbTxMapper">
    
    <resultMap type="UserQbTx" id="UserQbTxResult">
        <result property="id"    column="id"    />
        <result property="outBatchNo"    column="out_batch_no"    />
        <result property="txje"    column="txje"    />
        <result property="xm"    column="xm"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="txzt"    column="txzt"    />
        <result property="userId"    column="user_id"    />
        <result property="hdsj"    column="hdsj"    />
    </resultMap>

    <sql id="selectUserQbTxVo">
        select id, out_batch_no, txje, xm, cjsj, txzt,user_id from user_qb_tx
    </sql>

    <select id="selectUserQbTxList" parameterType="UserQbTx" resultMap="UserQbTxResult">
        <include refid="selectUserQbTxVo"/>
        <where>  
            <if test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>
            <if test="txje != null "> and txje = #{txje}</if>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="cjsj != null  and cjsj != ''"> and cjsj = #{cjsj}</if>
            <if test="txzt != null  and txzt != ''"> and txzt = #{txzt}</if>
        </where>
    </select>
    
    <select id="selectUserQbTxById" parameterType="Long" resultMap="UserQbTxResult">
        <include refid="selectUserQbTxVo"/>
        where id = #{id}
    </select>

    <select id="selectUserQbTxByOutBatchNo" parameterType="string" resultMap="UserQbTxResult">
        <include refid="selectUserQbTxVo"/>
        where out_batch_no = #{id}
    </select>
        
    <insert id="insertUserQbTx" parameterType="UserQbTx" useGeneratedKeys="true" keyProperty="id">
        insert into user_qb_tx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outBatchNo != null">out_batch_no,</if>
            <if test="txje != null">txje,</if>
            <if test="xm != null">xm,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="txzt != null">txzt,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="outBatchNo != null">#{outBatchNo},</if>
            <if test="txje != null">#{txje},</if>
            <if test="xm != null">#{xm},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="txzt != null">#{txzt},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateUserQbTx" parameterType="UserQbTx">
        update user_qb_tx
        <trim prefix="SET" suffixOverrides=",">
            <if test="outBatchNo != null">out_batch_no = #{outBatchNo},</if>
            <if test="txje != null">txje = #{txje},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="txzt != null">txzt = #{txzt},</if>
            <if test="hdsj != null">hdsj = #{hdsj},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserQbTxById" parameterType="Long">
        delete from user_qb_tx where id = #{id}
    </delete>

    <delete id="deleteUserQbTxByIds" parameterType="String">
        delete from user_qb_tx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>