<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.UserFkywMapper">
    
    <resultMap type="UserFkyw" id="UserFkywResult">
        <result property="id"    column="id"    />
        <result property="xm"    column="xm"    />
        <result property="sjh"    column="sjh"    />
        <result property="fklx"    column="fklx"    />
        <result property="bt"    column="bt"    />
        <result property="fknr"    column="fknr"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectUserFkywVo">
        select id, user_id,xm, sjh, fklx, bt, fknr, cjsj, del_flag from user_fkyw
    </sql>

    <select id="selectUserFkywList" parameterType="UserFkyw" resultMap="UserFkywResult">
        <include refid="selectUserFkywVo"/>
        <where>  
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="sjh != null  and sjh != ''"> and sjh = #{sjh}</if>
            <if test="fklx != null  and fklx != ''"> and fklx = #{fklx}</if>
            <if test="bt != null  and bt != ''"> and bt = #{bt}</if>
            <if test="fknr != null  and fknr != ''"> and fknr = #{fknr}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
        </where>
    </select>
    
    <select id="selectUserFkywById" parameterType="Long" resultMap="UserFkywResult">
        <include refid="selectUserFkywVo"/>
        where id = #{id} and del_flag=0
    </select>
        
    <insert id="insertUserFkyw" parameterType="UserFkyw">
        insert into user_fkyw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="xm != null">xm,</if>
            <if test="sjh != null">sjh,</if>
            <if test="fklx != null">fklx,</if>
            <if test="bt != null">bt,</if>
            <if test="fknr != null">fknr,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="xm != null">#{xm},</if>
            <if test="sjh != null">#{sjh},</if>
            <if test="fklx != null">#{fklx},</if>
            <if test="bt != null">#{bt},</if>
            <if test="fknr != null">#{fknr},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateUserFkyw" parameterType="UserFkyw">
        update user_fkyw
        <trim prefix="SET" suffixOverrides=",">
            <if test="xm != null">xm = #{xm},</if>
            <if test="sjh != null">sjh = #{sjh},</if>
            <if test="fklx != null">fklx = #{fklx},</if>
            <if test="bt != null">bt = #{bt},</if>
            <if test="fknr != null">fknr = #{fknr},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteUserFkywById" parameterType="Long">
        update user_fkyw set del_flag where id = #{id}
    </update>

    <delete id="deleteUserFkywByIds" parameterType="String">
        delete from user_fkyw where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>