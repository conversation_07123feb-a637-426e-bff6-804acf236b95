<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ninan.mapper.DdSbyqMapper">
    
    <resultMap type="DdSbyq" id="DdSbyqResult">
        <result property="id"    column="id"    />
        <result property="ddid"    column="ddid"    />
        <result property="sbid"    column="sbid"    />
        <result property="sxsl"    column="sxsl"    />
        <result property="sbzj"    column="sbzj"    />
        <result property="cjsj"    column="cjsj"    />
    </resultMap>
    <resultMap type="DdSb" id="DdSbResult">
        <result property="sbmc"    column="sbmc"    />
        <result property="sbdj"    column="sbdj"    />
    </resultMap>

    <sql id="selectDdSbyqVo">
        select id, ddid, sbid, sxsl, sbzj, cjsj from dd_sbyq
    </sql>

    <select id="selectDdSbyqList" parameterType="DdSbyq" resultMap="DdSbyqResult">
        <include refid="selectDdSbyqVo"/>
        <where>  
            <if test="ddid != null "> and ddid = #{ddid}</if>
            <if test="sbid != null "> and sbid = #{sbid}</if>
            <if test="sxsl != null "> and sxsl = #{sxsl}</if>
            <if test="sbzj != null "> and sbzj = #{sbzj}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
             and del_flag = 0
        </where>
    </select>
    
    <select id="selectDdSbyqById" parameterType="Long" resultMap="DdSbyqResult">
        <include refid="selectDdSbyqVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectDdSbById" parameterType="Long" resultMap="DdSbResult">
        select sbmc, sbdj from dd_sb where id = #{id} and del_flag = 0
    </select>
        
    <insert id="insertDdSbyq" parameterType="DdSbyq" useGeneratedKeys="true" keyProperty="id">
        insert into dd_sbyq
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ddid != null">ddid,</if>
            <if test="sbid != null">sbid,</if>
            <if test="sxsl != null">sxsl,</if>
            <if test="sbzj != null">sbzj,</if>
            <if test="cjsj != null">cjsj,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ddid != null">#{ddid},</if>
            <if test="sbid != null">#{sbid},</if>
            <if test="sxsl != null">#{sxsl},</if>
            <if test="sbzj != null">#{sbzj},</if>
            <if test="cjsj != null">#{cjsj},</if>
         </trim>
    </insert>


    <insert id="batchInsertDdSbyq" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into dd_sbyq
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].ddid != null">ddid,</if>
            <if test="list[0].sbid != null">sbid,</if>
            <if test="list[0].sxsl != null">sxsl,</if>
            <if test="list[0].sbzj != null">sbzj,</if>
            <if test="list[0].cjsj != null">cjsj,</if>
        </trim>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.ddid != null">#{item.ddid},</if>
                <if test="item.sbid != null">#{item.sbid},</if>
                <if test="item.sxsl != null">#{item.sxsl},</if>
                <if test="item.sbzj != null">#{item.sbzj},</if>
                <if test="item.cjsj != null">#{item.cjsj},</if>
            </trim>
        </foreach>
    </insert>


    <update id="updateDdSbyq" parameterType="DdSbyq">
        update dd_sbyq
        <trim prefix="SET" suffixOverrides=",">
            <if test="ddid != null">ddid = #{ddid},</if>
            <if test="sbid != null">sbid = #{sbid},</if>
            <if test="sxsl != null">sxsl = #{sxsl},</if>
            <if test="sbzj != null">sbzj = #{sbzj},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteDdSbyqById" parameterType="Long">
        update dd_sbyq set del_flag=2 where ddid = #{id}
    </update>

    <delete id="deleteDdSbyqByIds" parameterType="String">
        update dd_sbyq set del_flag=2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>