# Tomcat
server:
  port: 8090

# Spring
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  application:
    # 应用名称 您安小程序模块
    name: ruoyi-ninan
  profiles:
    # 环境配置  dev(测试环境)  druid(正式环境)
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: *************:8848
        username: nacos
        password: ninan123
#        ip: ${spring.cloud.nacos.discovery.ip}
      config:
        # 配置中心地址
        server-addr: *************:8848
        username: nacos
        password: ninan123
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
