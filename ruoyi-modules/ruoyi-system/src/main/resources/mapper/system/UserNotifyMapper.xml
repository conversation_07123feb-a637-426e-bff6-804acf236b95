<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserNotifyMapper">
    
    <resultMap type="UserNotify" id="UserNotifyResult">
        <result property="id"    column="id"    />
        <result property="nr"    column="nr"    />
        <result property="fsrid"    column="fsrid"    />
        <result property="jsrid"    column="jsrid"    />
        <result property="ydwd"    column="ydwd"    />
        <result property="lx"    column="lx"    />
        <result property="xxlx"    column="xxlx"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="xm"    column="xm"    />
        <result property="xxz"    column="xxz"    />
        <result property="wdsl"    column="wdsl"    />
    </resultMap>

    <sql id="selectUserNotifyVo">
        select id, nr, fsrid, jsrid, ydwd, lx, xxlx, cjsj, del_flag from `ry-naf`.`user_notify`
    </sql>

    <select id="selectUserNotifyList" parameterType="UserNotify" resultMap="UserNotifyResult">
        <include refid="selectUserNotifyVo"/>
        <where>  
            <if test="nr != null  and nr != ''"> and nr = #{nr}</if>
            <if test="fsrid != null "> and fsrid = #{fsrid}</if>
            <if test="jsrid != null "> and jsrid = #{jsrid}</if>
            <if test="ydwd != null  and ydwd != ''"> and ydwd = #{ydwd}</if>
            <if test="xxlx != null  and xxlx != ''"> and xxlx = #{xxlx}</if>
            <if test="cjsj != null "> and cjsj = #{cjsj}</if>
        </where>
    </select>

    <select id="selectKfLbList" resultMap="UserNotifyResult">
        SELECT xm, xxz, MAX(n.cjsj)cjsj, SUM(CASE WHEN n.ydwd = '0' THEN 1 ELSE 0 END) AS wdsl,
               n.fsrid, openid as jsrid from `ry-naf`.`user_notify` n LEFT JOIN `ry-naf`.`user_info` w on w.id=n.fsrid WHERE n.lx=0 and w.xm is NOT NULL GROUP BY fsrid ORDER BY cjsj desc
    </select>

    <select id="ltjlList" resultMap="UserNotifyResult">
        SELECT * FROM `ry-naf`.`user_notify` WHERE fsrid = #{fsrid} or jsrid = #{jsrid} AND lx = 0 order by cjsj desc
    </select>

    <update id="updatYd">
        update `ry-naf`.`user_notify` set ydwd = 1 where fsrid = #{fsrid} or jsrid = #{jsrid} and ydwd = 0 and lx = 0
    </update>

    <select id="selectUserNotifyById" parameterType="Long" resultMap="UserNotifyResult">
        <include refid="selectUserNotifyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserNotify" parameterType="UserNotify" useGeneratedKeys="true" keyProperty="id">
        insert into `ry-naf`.`user_notify`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nr != null">nr,</if>
            <if test="fsrid != null">fsrid,</if>
            <if test="jsrid != null">jsrid,</if>
            <if test="ydwd != null">ydwd,</if>
            <if test="xxlx != null">xxlx,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nr != null">#{nr},</if>
            <if test="fsrid != null">#{fsrid},</if>
            <if test="jsrid != null">#{jsrid},</if>
            <if test="ydwd != null">#{ydwd},</if>
            <if test="xxlx != null">#{xxlx},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateUserNotify" parameterType="UserNotify">
        update `ry-naf`.`user_notify`
        <trim prefix="SET" suffixOverrides=",">
            <if test="nr != null">nr = #{nr},</if>
            <if test="fsrid != null">fsrid = #{fsrid},</if>
            <if test="jsrid != null">jsrid = #{jsrid},</if>
            <if test="ydwd != null">ydwd = #{ydwd},</if>
            <if test="xxlx != null">xxlx = #{xxlx},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserNotifyById" parameterType="Long">
        delete from `ry-naf`.`user_notify` where id = #{id}
    </delete>

    <delete id="deleteUserNotifyByIds" parameterType="String">
        delete from `ry-naf`.`user_notify` where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>