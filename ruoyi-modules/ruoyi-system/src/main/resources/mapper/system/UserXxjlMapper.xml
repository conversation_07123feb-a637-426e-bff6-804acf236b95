<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserXxjlMapper">
    
    <resultMap type="UserXxjl" id="UserXxjlResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="zqsl"    column="zqsl"    />
        <result property="cwsl"    column="cwsl"    />
        <result property="xm"    column="xm"    />
        <result property="unionid"    column="unionid"    />
        <result property="zql"    column="zql"    />
        <result property="sftg"    column="sftg"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="nrbh"    column="nrbh"    />
        <result property="lx"    column="lx"    />
    </resultMap>

    <sql id="selectUserXxjlVo">
        select id, user_id, zqsl, cwsl, xm, unionid, zql, sftg, cjsj, nrbh, lx from user_xxjl
    </sql>

    <select id="selectUserXxjlList" parameterType="UserXxjl" resultMap="UserXxjlResult">
        <include refid="selectUserXxjlVo"/>
        <where>  
            <if test="xm != null  and xm != ''"> and xm like concat('%', #{xm}, '%')</if>
            <if test="sftg != null  and sftg != ''"> and sftg = #{sftg}</if>
            <if test="nrbh != null  and nrbh != ''"> and nrbh = #{nrbh}</if>
            <if test="lx != null  and lx != ''"> and lx = #{lx}</if>
            <if test="params.beginTime != null  and params.beginTime != ''"> and cjsj between #{params.beginTime} and #{params.endTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectUserXxjlById" parameterType="Long" resultMap="UserXxjlResult">
        <include refid="selectUserXxjlVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserXxjl" parameterType="UserXxjl" useGeneratedKeys="true" keyProperty="id">
        insert into user_xxjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="zqsl != null">zqsl,</if>
            <if test="cwsl != null">cwsl,</if>
            <if test="xm != null">xm,</if>
            <if test="unionid != null">unionid,</if>
            <if test="zql != null">zql,</if>
            <if test="sftg != null">sftg,</if>
            <if test="cjsj != null">cjsj,</if>
            <if test="nrbh != null">nrbh,</if>
            <if test="lx != null">lx,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="zqsl != null">#{zqsl},</if>
            <if test="cwsl != null">#{cwsl},</if>
            <if test="xm != null">#{xm},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="zql != null">#{zql},</if>
            <if test="sftg != null">#{sftg},</if>
            <if test="cjsj != null">#{cjsj},</if>
            <if test="nrbh != null">#{nrbh},</if>
            <if test="lx != null">#{lx},</if>
         </trim>
    </insert>

    <update id="updateUserXxjl" parameterType="UserXxjl">
        update user_xxjl
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="zqsl != null">zqsl = #{zqsl},</if>
            <if test="cwsl != null">cwsl = #{cwsl},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="zql != null">zql = #{zql},</if>
            <if test="sftg != null">sftg = #{sftg},</if>
            <if test="cjsj != null">cjsj = #{cjsj},</if>
            <if test="nrbh != null">nrbh = #{nrbh},</if>
            <if test="lx != null">lx = #{lx},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserXxjlById" parameterType="Long">
        delete from user_xxjl where id = #{id}
    </delete>

    <delete id="deleteUserXxjlByIds" parameterType="String">
        delete from user_xxjl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>