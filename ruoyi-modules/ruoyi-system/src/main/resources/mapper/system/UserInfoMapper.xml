<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserInfoMapper">

    <resultMap type="UserInfo" id="UserInfoResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="xm"    column="xm"    />
        <result property="sfzh"    column="sfzh"    />
        <result property="xxz"    column="xxz"    />
        <result property="sfzzm"    column="sfzzm"    />
        <result property="sfzfm"    column="sfzfm"    />
        <result property="shzt"    column="shzt"    />
        <result property="smrz"    column="smrz"    />
        <result property="xzz"    column="xzz"    />
        <result property="khh"    column="khh"    />
        <result property="yhh"    column="yhh"    />
        <result property="jn"    column="jn"    />
        <result property="sg"    column="sg"    />
        <result property="tz"    column="tz"    />
        <result property="sfjr"    column="sfjr"    />
        <result property="wysp"    column="wysp"    />
        <result property="xb"    column="xb"    />
        <result property="sjh"    column="sjh"    />
        <result property="sfzdz"    column="sfzdz"    />
        <result property="wxaCodeUrl"    column="wxa_code_url"    />
        <result property="sf"    column="sf"    />
        <result property="zsyg"    column="zsyg"    />
        <result property="dqzt"    column="dqzt"    />
        <result property="qyzt"    column="qyzt"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserInfoVo">
        SELECT
            user_info.id,
            xm,
            sfzh,
            sfzzm,
            sfzfm,
            shzt,
            xb,
            sjh,
            sfzdz,
            wxa_code_url,
            create_time,
            update_time,
            sf,
            zsyg,
            xzz,
            khh,
            yhh,
            jn,
            sg,
            CASE

                WHEN sf = 2
                    AND dqzt = 2 THEN
                    3 ELSE dqzt
                END AS dqzt,
            smrz,
            wysp,
            sfjr,
            tz,(
                SELECT
                    shzt
                FROM
                    `ry-naf`.`user_company`
                WHERE
                    user_company.user_id = user_info.id
                       LIMIT 1
            ) AS qyzt,
		txz AS xxz
        FROM
            `ry-naf`.`user_info`

    </sql>

    <select id="selectUserInfoList" parameterType="UserInfo" resultMap="UserInfoResult">
        <include refid="selectUserInfoVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="xm != null  and xm != ''"> and xm like concat('%', #{xm}, '%')</if>
            <if test="sfzh != null  and sfzh != ''"> and sfzh = #{sfzh}</if>
            <if test="xxz != null  and xxz != ''"> and xxz = #{xxz}</if>
            <if test="sfzzm != null  and sfzzm != ''"> and sfzzm = #{sfzzm}</if>
            <if test="sfzfm != null  and sfzfm != ''"> and sfzfm = #{sfzfm}</if>
            <if test="shzt != null  and shzt != ''"> and shzt = #{shzt}</if>
            <if test="smrz != null  and smrz != ''"> and smrz = #{smrz}</if>
            <if test="xzz != null  and xzz != ''"> and xzz = #{xzz}</if>
            <if test="khh != null  and khh != ''"> and khh = #{khh}</if>
            <if test="yhh != null  and yhh != ''"> and yhh = #{yhh}</if>
            <if test="jn != null  and jn != ''"> and jn = #{jn}</if>
            <if test="sg != null  and sg != ''"> and sg = #{sg}</if>
            <if test="tz != null  and tz != ''"> and tz = #{tz}</if>
            <if test="sfjr != null  and sfjr != ''"> and sfjr = #{sfjr}</if>
            <if test="wysp != null  and wysp != ''"> and wysp = #{wysp}</if>
            <if test="xb != null  and xb != ''"> and xb = #{xb}</if>
            <if test="sjh != null  and sjh != ''"> and sjh like concat('%', #{sjh}, '%')</if>
            <if test="sfzdz != null  and sfzdz != ''"> and sfzdz = #{sfzdz}</if>
            <if test="wxaCodeUrl != null  and wxaCodeUrl != ''"> and wxa_code_url = #{wxaCodeUrl}</if>
            <if test="sf != null  and sf != ''"> and sf = #{sf}</if>
            <if test="zsyg != null  and zsyg != ''"> and zsyg = #{zsyg}</if>
            <if test="dqzt != null  and dqzt != ''"> and dqzt = #{dqzt}</if>
            <if test="bigDate != null  and endDate != null"> and create_time BETWEEN  #{bigDate}  and #{endDate} </if>
             and xm is not null
             and del_flag=0
        </where>
        GROUP BY user_info.id
    </select>



    <select id="selectUserInfoListExport" parameterType="UserInfo" resultMap="UserInfoResult">
        SELECT
        user_info.xm,
        user_wxa.appid,
        user_wxa.openid
        FROM
        `ry-naf`.`user_info`
        LEFT JOIN `ry-naf`.`user_wxa` ON user_info.unionid = user_wxa.unionid
        <where>
            <if test="xm != null  and xm != ''"> and xm like concat('%', #{xm}, '%')</if>
            <if test="sfzh != null  and sfzh != ''"> and sfzh = #{sfzh}</if>
            <if test="xxz != null  and xxz != ''"> and xxz = #{xxz}</if>
            <if test="sfzzm != null  and sfzzm != ''"> and sfzzm = #{sfzzm}</if>
            <if test="sfzfm != null  and sfzfm != ''"> and sfzfm = #{sfzfm}</if>
            <if test="shzt != null  and shzt != ''"> and shzt = #{shzt}</if>
            <if test="smrz != null  and smrz != ''"> and smrz = #{smrz}</if>
            <if test="xzz != null  and xzz != ''"> and xzz = #{xzz}</if>
            <if test="khh != null  and khh != ''"> and khh = #{khh}</if>
            <if test="yhh != null  and yhh != ''"> and yhh = #{yhh}</if>
            <if test="jn != null  and jn != ''"> and jn = #{jn}</if>
            <if test="sg != null  and sg != ''"> and sg = #{sg}</if>
            <if test="tz != null  and tz != ''"> and tz = #{tz}</if>
            <if test="sfjr != null  and sfjr != ''"> and sfjr = #{sfjr}</if>
            <if test="wysp != null  and wysp != ''"> and wysp = #{wysp}</if>
            <if test="xb != null  and xb != ''"> and xb = #{xb}</if>
            <if test="sjh != null  and sjh != ''"> and sjh like concat('%', #{sjh}, '%')</if>
            <if test="sfzdz != null  and sfzdz != ''"> and sfzdz = #{sfzdz}</if>
            <if test="wxaCodeUrl != null  and wxaCodeUrl != ''"> and wxa_code_url = #{wxaCodeUrl}</if>
            <if test="sf != null  and sf != ''"> and sf = #{sf}</if>
            <if test="zsyg != null  and zsyg != ''"> and zsyg = #{zsyg}</if>
            <if test="dqzt != null  and dqzt != ''"> and dqzt = #{dqzt}</if>
        </where>
        GROUP BY user_info.id, user_info.xm, user_wxa.appid, user_wxa.openid
    </select>








    <select id="selectUserInfoById" parameterType="Long" resultMap="UserInfoResult">
        <include refid="selectUserInfoVo"/>
        where user_info.id = #{id}
    </select>

    <select id="selectUserGzhOpenid" resultType="map">
        SELECT
            mp1_openid mp1Openid,openid
        FROM
            `ry-naf`.`user_wxa`
                LEFT JOIN `ry-naf`.`user_mp` ON `ry-naf`.`user_wxa`.unionid = `ry-naf`.`user_mp`.unionid
        WHERE
            `ry-naf`.`user_wxa`.user_id =#{id}
            LIMIT 1
    </select>

    <insert id="insertUserInfo" parameterType="UserInfo" useGeneratedKeys="true" keyProperty="id">
        insert into `ry-naf`.`user_info`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unionid != null">unionid,</if>
            <if test="xm != null">xm,</if>
            <if test="sfzh != null">sfzh,</if>
            <if test="xxz != null">xxz,</if>
            <if test="sfzzm != null">sfzzm,</if>
            <if test="sfzfm != null">sfzfm,</if>
            <if test="shzt != null">shzt,</if>
            <if test="smrz != null">smrz,</if>
            <if test="xzz != null">xzz,</if>
            <if test="khh != null">khh,</if>
            <if test="yhh != null">yhh,</if>
            <if test="jn != null">jn,</if>
            <if test="sg != null">sg,</if>
            <if test="tz != null">tz,</if>
            <if test="sfjr != null">sfjr,</if>
            <if test="wysp != null">wysp,</if>
            <if test="xb != null">xb,</if>
            <if test="sjh != null">sjh,</if>
            <if test="sfzdz != null">sfzdz,</if>
            <if test="wxaCodeUrl != null">wxa_code_url,</if>
            <if test="sf != null">sf,</if>
            <if test="zsyg != null">zsyg,</if>
            <if test="dqzt != null">dqzt,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unionid != null">#{unionid},</if>
            <if test="xm != null">#{xm},</if>
            <if test="sfzh != null">#{sfzh},</if>
            <if test="xxz != null">#{xxz},</if>
            <if test="sfzzm != null">#{sfzzm},</if>
            <if test="sfzfm != null">#{sfzfm},</if>
            <if test="shzt != null">#{shzt},</if>
            <if test="smrz != null">#{smrz},</if>
            <if test="xzz != null">#{xzz},</if>
            <if test="khh != null">#{khh},</if>
            <if test="yhh != null">#{yhh},</if>
            <if test="jn != null">#{jn},</if>
            <if test="sg != null">#{sg},</if>
            <if test="tz != null">#{tz},</if>
            <if test="sfjr != null">#{sfjr},</if>
            <if test="wysp != null">#{wysp},</if>
            <if test="xb != null">#{xb},</if>
            <if test="sjh != null">#{sjh},</if>
            <if test="sfzdz != null">#{sfzdz},</if>
            <if test="wxaCodeUrl != null">#{wxaCodeUrl},</if>
            <if test="sf != null">#{sf},</if>
            <if test="zsyg != null">#{zsyg},</if>
            <if test="dqzt != null">#{dqzt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserInfo" parameterType="UserInfo">
        update `ry-naf`.`user_info`
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="sfzh != null">sfzh = #{sfzh},</if>
            <if test="xxz != null">xxz = #{xxz},</if>
            <if test="sfzzm != null">sfzzm = #{sfzzm},</if>
            <if test="sfzfm != null">sfzfm = #{sfzfm},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="smrz != null">smrz = #{smrz},</if>
            <if test="xzz != null">xzz = #{xzz},</if>
            <if test="khh != null">khh = #{khh},</if>
            <if test="yhh != null">yhh = #{yhh},</if>
            <if test="jn != null">jn = #{jn},</if>
            <if test="sg != null">sg = #{sg},</if>
            <if test="tz != null">tz = #{tz},</if>
            <if test="sfjr != null">sfjr = #{sfjr},</if>
            <if test="wysp != null">wysp = #{wysp},</if>
            <if test="xb != null">xb = #{xb},</if>
            <if test="sjh != null">sjh = #{sjh},</if>
            <if test="sfzdz != null">sfzdz = #{sfzdz},</if>
            <if test="wxaCodeUrl != null">wxa_code_url = #{wxaCodeUrl},</if>
            <if test="sf != null">sf = #{sf},</if>
            <if test="zsyg != null">zsyg = #{zsyg},</if>
            <if test="dqzt != null">dqzt = #{dqzt},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateQySh" parameterType="Long">
        UPDATE `ry-naf`.`user_company` set  shzt=#{shzt} where user_id=#{userId}
    </update>

    <delete id="deleteUserInfoById" parameterType="Long">
        delete from `ry-naf`.`user_info` where id = #{id}
    </delete>

    <delete id="deleteUserInfoByIds" parameterType="String">
        delete from `ry-naf`.`user_info` where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>