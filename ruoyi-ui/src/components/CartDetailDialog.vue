<!--
 * @Description: 
 * @Author: 小刘
 * @Date: 2025-03-26 16:03:37
 * @LastEditTime: 2025-03-27 16:15:46
 * @LastEditors: 小刘
-->
<template>
    <div>
        <el-dialog
        :visible.sync="showDialog"
        width="50rem"
        center
        append-to-body
       >
       <el-descriptions class="margin-top" title="总支出" :column="3"  cemter border>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-data-board"></i>
        平台方分成
      </template>
      {{ CountData.ptfc ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-user"></i>
        团队利益
      </template>
      {{ CountData.sjfy ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-sugar"></i>
        发放劳务费
      </template>
      {{ CountData.gz ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-checked"></i>
        新人红包(可提现)
      </template>
      {{ CountData.jljye||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-delete"></i>
        新人红包(不可提现)
      </template>
      {{ CountData.jlj_ktx||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-data-analysis"></i>
        平台余额
      </template>
      {{ CountData.ptye||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-remove"></i>
        未提金额
      </template>
      {{ CountData.wtje||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-postcard"></i>
        支出总汇
      </template>
      {{ CountData.zzc ||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-office-building"></i>
        余额变更
      </template>
      {{ CountData.yebg||'0'  }}
    </el-descriptions-item>
  </el-descriptions>
       </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        showDialog: {
          type: Boolean,    
        },
        CountData:{
            type:Object
        },
  },
  data() {
    return {
     
    };
  },
  methods: {
    
  }
};
</script> 
