<!--
 * @Description: 
 * @Author: 小刘
 * @Date: 2025-03-26 11:11:40
 * @LastEditTime: 2025-03-27 14:28:12
 * @LastEditors: 小刘
-->

<template>
    <div>
        <el-card shadow="hover">
            <div slot="header" class="clearfix">
              <span class="color">
                <slot name="top_title">	
                
			           </slot>
            </span>
            </div>
            <div class="content">
              <div class="left">
                 <span>￥</span> 
                 <slot name="Count">	
                 </slot>
            </div>
              <div class="right">
                <slot name="bottom_button">	
                </slot>
              </div>
            </div>
          </el-card>
    </div>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  height: 4.375rem;
  .left {
    margin-top: 0.9375rem;
    width: 80%;
    font-size: 2rem;
    span {
      font-size: 0.875rem;
    }
  }
  .right {
    margin-top: 1.5625rem;
    font-size: 0.9375rem;
  }
}
.color {
  color: #fff;
}
.position {
  text-align: center;
  color: #474747;
  font-size: 1rem;
}
::v-deep .el-card {
  border-radius: 1.25rem;
}
::v-deep .el-card__header {
  background-color: #409eff;
}
::v-deep .el-card__body {
  padding: 0px 0.75rem 0px 0.75rem;
}
.clearfix {
  display: flex;
  .recharge {
    flex: right;
    margin-left: 65%;
    ::v-deep .el-button--text {
      color: #fff;
      margin-top: -1.25rem;
    }
  }
}
</style>