<template>
    <div>
        <el-dialog title="充值" :visible.sync="show_recharge_dialog" width="50rem">
            <el-form :model="form" ref="ruleForm" :rules="rules" >
                <el-form-item label="充值金额"  prop="money" label-width="120px">
                    <el-input 
                    v-model="form.money"  
                    type="number"
                    clearable>
                        <template slot="append">元</template>
                    </el-input>
                </el-form-item>
                
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="Rechargecancel">取 消</el-button>
                <el-button type="primary" @click="RechargeConfirm()">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {Recharge} from '@/api/login.js'
export default {
    
    props: {
        show_recharge_dialog: {
           type: Boolean,    
        },
       
  },
  data() {
    return {
        form:{
            money:''
        },
        rules:{
            money: [
            { required: true, message: '请输入充值金额', trigger: 'change' }
          ],
        }
    };
  },
  methods: {
    Rechargecancel(){
        this.form={
            money:''
        }
        this.$emit('Rechargecancel')
        
    },
    RechargeConfirm(){
        this.$emit('RechargeConfirm', this.$refs['ruleForm'],this.form.money)
    }
  }
};
</script>