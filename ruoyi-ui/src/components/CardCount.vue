<template>
    <div>
        <el-row :gutter="15">
            <el-col :span="6">
                <cardSlot>
                <template #top_title>
                    总收入(元)
                </template>
                <template #Count>
                    {{ CountData.yhcz||'0' }}
                </template>
                <template #bottom_button>
                    <keep-alive>
                    <el-button
                        type="text"
                        class="orderTimeDetail"
                        @click="getIncome()"
                    >
                        查看详情
                    </el-button>
                    </keep-alive>
                </template>
                </cardSlot>
            </el-col>
            <el-col :span="6">
                <cardSlot>
                <template #top_title>
                    总支出(元)
                </template>
                <template #Count>
                    {{ CountData.zzc ||'0' }}
                </template>
                <template #bottom_button>
                    <keep-alive>
                    <el-button
                        type="text"
                        class="orderTimeDetail"
                        @click="getExpand()"
                    >
                        查看详情
                    </el-button>
                    </keep-alive>
                </template>
                </cardSlot>
            </el-col>
            <el-col :span="6">
                <cardSlot>
                <template #top_title>
                    利润(元)
                </template>
                <template #Count>
                    {{ CountData.lr  ||'0' }}
                </template>
               
                </cardSlot>
            </el-col>
            <el-col :span="6">
                <cardSlot>
                <template #top_title >
                    商户余额(元)
                    <span class="right_button" @click="recharge">
                       充值
                    </span>
                </template>
                <template #Count>
                    {{ CountData.zhye  ||'0' }}
                    
                </template>
                
                <template #bottom_button>
                    <keep-alive>
                    <el-button
                        type="text"
                        class="orderTimeDetail"
                        @click="accountBalance()"
                    >
                        查看明细
                    </el-button>
                   
                    </keep-alive>
                </template>
                </cardSlot>
            </el-col>
    </el-row>
    <!-- 弹窗 -->
    <!-- <CartDetailDialog
    :showDialog="ShowDialog"
    :CountData="CountData"
    
    >
    </CartDetailDialog> -->
    <!-- 详情 -->
    <el-dialog
        :visible.sync="showDialog"
        width="50rem"
        center
        append-to-body
       >
       <el-descriptions class="margin-top" title="总支出" :column="3"  cemter border>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-data-board"></i>
        平台方分成
      </template>
      {{ CountData.ptfc ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-user"></i>
        团队利益
      </template>
      {{ CountData.sjfy ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-sugar"></i>
        发放劳务费
      </template>
      {{ CountData.gz ||'0' }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-checked"></i>
        新人红包(可提现)
      </template>
      {{ CountData.jljye||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-delete"></i>
        新人红包(不可提现)
      </template>
      {{ CountData.jlj_ktx||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-data-analysis"></i>
        平台余额
      </template>
      {{ CountData.ptye||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-document-remove"></i>
        未提金额
      </template>
      {{ CountData.wtje||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-postcard"></i>
        支出总汇
      </template>
      {{ CountData.zzc ||'0'  }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">
        <i class="el-icon-office-building"></i>
        余额变更
      </template>
      {{ CountData.yebg||'0'  }}
    </el-descriptions-item>
  </el-descriptions>
       </el-dialog>
    </div>
</template>
<script>
 import CartDetailDialog from './CartDetailDialog.vue'
 import cardSlot from '@/components/CardSlot.vue'
 export default {
  name: "Index",
  components:{cardSlot,CartDetailDialog},
  props:{
        CountData:{
            type:Object
        },
        // showDialog: {
        //      type: Boolean,
        // },
    },
  data() { 
    return {
        showDialog:false
    }
  },
  
  methods:{
    // 总收入详情
    getIncome(){
        this.$emit('getIncome')
    },
    // 总支出详情
    getExpand(){
        // this.$emit('getExpand')
        this.showDialog=true
       
    },
    // 利润详情
    getprofit(){
        this.$emit('getprofit')
    },
    // 商户余额详情
    accountBalance(){
        this.$emit('accountBalance')
    },
    // 充值
    recharge(){
        this.$emit('recharge')
    },
    beforeClose(){
        this.$emit('beforeClose')
    }
  }
  }

</script>
<style lang="scss" scoped>
.right_button{
    margin-left: 12vw;
}
.right_button:active{
    color: #ccc;
    text-decoration: underline;
}
</style>