
import request from '@/utils/request'

// 查询订单列表
export function listDd(query) {
  return request({
    url: '/system/dd/list',
    method: 'get',
    params: query
  })
}
// 审核订单
export function listDdExame(query) {
  return request({
    url: '/system/dd/ddsh',
    method: 'get',
    params: query
  })
}
// 查询订单详细
export function getDd(id) {
  return request({
    url: '/system/dd/' + id,
    method: 'get'
  })
}

// 新增订单
export function addDd(data) {
  return request({
    url: '/system/dd',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateDd(data) {
  return request({
    url: '/system/dd',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delDd(id) {
  return request({
    url: '/system/dd/' + id,
    method: 'delete'
  })
}
/* 订单详情 */
export function getDdxq(query) {
  return request({
    url: '/system/dd/getInfo' ,
    params: query,
    method: 'get'
  })
}
// 审核类型 
export function getDdddLx(query) {
  return request({
    url: '/system/dd/selectShlxList' ,
    params: query,
    method: 'get'
  })
}