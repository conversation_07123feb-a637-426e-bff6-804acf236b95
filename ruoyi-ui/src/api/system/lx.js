/*
 * @Description: 
 * @Author: 小刘
 * @Date: 2025-03-04 15:41:47
 * @LastEditTime: 2025-03-04 16:36:56
 * @LastEditors: 小刘
 */
import request from '@/utils/request'

// 查询审核类型列表
export function listLx(query) {
  return request({
    url: '/system/lx/list',
    method: 'get',
    params: query
  })
}

// 查询审核类型详细
export function getLx(id) {
  return request({
    url: '/system/lx/' + id,
    method: 'get'
  })
}

// 新增审核类型
export function addLx(data) {
  return request({
    url: '/system/lx',
    method: 'post',
    data: data
  })
}

// 修改审核类型
export function updateLx(data) {
  return request({
    url: '/system/lx',
    method: 'put',
    data: data
  })
}

// 删除审核类型
export function delLx(id) {
  return request({
    url: '/system/lx/' + id,
    method: 'delete'
  })
}
// 审核人列表 
export function userList(query) {
  return request({
    url: '/system/lx/selectUserShList',
    method: 'get',
    params: query
  })
}