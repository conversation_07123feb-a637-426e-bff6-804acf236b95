/*
 * @Description: 
 * @Author: 小刘
 * @Date: 2025-01-13 09:08:06
 * @LastEditTime: 2025-03-17 11:16:42
 * @LastEditors: 小刘
 */
import request from '@/utils/request'

// 查询微信小程序用户信息列表
export function listWxa(query) {
  return request({
    url: '/system/wxa/list',
    method: 'get',
    params: query
  })
}

// 用户审核 
export function listWxaExame(query) {
  return request({
    url: '/system/wxa/yhsh',
    method: 'get',
    params: query
  })
}
// 企业审核 
export function listWxaExameqy(query) {
  return request({
    url: '/system/wxa/qyrz',
    method: 'get',
    params: query
  })
}
// 查询微信小程序用户信息详细
export function getWxa(id) {
  return request({
    url: '/system/wxa/' + id,
    method: 'get'
  })
}

// 新增微信小程序用户信息
export function addWxa(data) {
  return request({
    url: '/system/wxa',
    method: 'post',
    data: data
  })
}

// 修改微信小程序用户信息
export function updateWxa(data) {
  return request({
    url: '/system/wxa',
    method: 'put',
    data: data
  })
}

// 删除微信小程序用户信息
export function delWxa(id) {
  return request({
    url: '/system/wxa/' + id,
    method: 'delete'
  })
}
// 升级用户 
export function upUser(query) {
  return request({
    url: '/system/wxa/updateSjZsYg',
    method: 'post',
    data: query
  })
}