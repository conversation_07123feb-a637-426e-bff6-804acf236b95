import request from '@/utils/request'

// 查询明细列表
export function listQbMx(query) {
  return request({
    url: '/system/qbMx/list',
    method: 'get',
    params: query
  })
}

// 查询明细详细
export function getQbMx(id) {
  return request({
    url: '/system/qbMx/' + id,
    method: 'get'
  })
}

// 新增明细
export function addQbMx(data) {
  return request({
    url: '/system/qbMx',
    method: 'post',
    data: data
  })
}

// 修改明细
export function updateQbMx(data) {
  return request({
    url: '/system/qbMx',
    method: 'put',
    data: data
  })
}

// 删除明细
export function delQbMx(id) {
  return request({
    url: '/system/qbMx/' + id,
    method: 'delete'
  })
}
