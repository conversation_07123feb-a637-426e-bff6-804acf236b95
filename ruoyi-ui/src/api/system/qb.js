import request from '@/utils/request'

// 查询小程序用户钱包列表
export function listQb(query) {
  return request({
    url: '/system/qb/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户钱包详细
export function getQb(id) {
  return request({
    url: '/system/qb/' + id,
    method: 'get'
  })
}

// 新增小程序用户钱包
export function addQb(data) {
  return request({
    url: '/system/qb',
    method: 'post',
    data: data
  })
}

// 修改小程序用户钱包
export function updateQb(data) {
  return request({
    url: '/system/qb',
    method: 'put',
    data: data
  })
}

// 删除小程序用户钱包
export function delQb(id) {
  return request({
    url: '/system/qb/' + id,
    method: 'delete'
  })
}
