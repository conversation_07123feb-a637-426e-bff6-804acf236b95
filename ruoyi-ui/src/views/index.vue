
<template>
  <div class="app-container home" v-if="roles=='admin'||roles=='FD'">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="4.25rem"
    >
      <el-form-item label="时间范围">
         <div class="block">
          <el-date-picker
            v-model="queryParams.time"
            :picker-options="pickerOptions"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :unlink-panels="true"
            :default-time="['00:00:00', '23:59:59']">
          >
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 收支统计卡片 -->
      <CardCount
      :CountData="CountData"
      @getIncome="getIncome"
      @getExpand="getExpand"
      @accountBalance="accountBalance"
      @recharge="recharge"
      >
      </CardCount>
    <!-- 图表展示 -->
    <el-row
        :gutter="20"
        class="echarts-container"
        style="margin-bottom: 1.25rem; margin-top: 0.9375rem; width: 100%"
        id="echarts"
      >
        <el-card shadow="hover">
          <div slot="header" class="clearfix1">
            <span class="White">支出收入量(近{{Monthlength||'0'}}个月支出收入统计)</span>
            <el-col style="margin-top: 0.625rem; height: 20rem">
              <div >
                <div id="echart" />
              </div>
            </el-col>
          </div>
        </el-card>
      </el-row>
      <!-- 充值 -->
      <Card_recharge_dialog
      :show_recharge_dialog="show_recharge_dialog"
      @RechargeConfirm="RechargeConfirm"
      @Rechargecancel="Rechargecancel"
      >
     </Card_recharge_dialog>
  </div>
</template>

<script>
  import Card_recharge_dialog from '@/components/Card_recharge_dialog.vue'
  import CardCount from '@/components/CardCount.vue'
  import elementResizeDetectorMaker from "element-resize-detector";
  import "@/utils/chart.resize";
  import * as echarts from "echarts";
  import {EchatsData,Recharge} from '@/api/login.js'
  export default {
  name: "Index",
  components:{CardCount, Card_recharge_dialog},
  data() {
    return {
      roles:'',
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      queryParams:{
        time:''
      },
      Time:[],
      Income:[],
      Expend:[],
      CountData:{},
      showDialog:false,
      show_recharge_dialog:false,
      Monthlength:0
      
    };
  },
  created(){
    this.roles=this.$store.state.user.name
    
  },
  methods: {
    // 搜索
    handleQuery(){
      this.day10OutAndInCome()
    },

    // 总收入详情
    getIncome(){
        
    },
    // 总支出详情
    getExpand(){
       this.showDialog=true
      
    },
    // 关闭
    beforeClose(){
      this.showDialog=false
    },
    // 商户余额详情
    accountBalance(){
        this.$router.push({
          path:'/MerchantDetails'
        })
    },
    // 充值
    recharge(){
      
      this.show_recharge_dialog=true
    },
    Rechargecancel(){
      this.show_recharge_dialog=false
      
    },
    RechargeConfirm(formName,val){
      formName.validate(async(valid) => {
          if (valid) {
            const res=await Recharge({money:val})
            this.show_recharge_dialog=false
            this.day10OutAndInCome()
            this.$modal.msgSuccess(`${res.msg}`);
            
          } 
        });
      
     
    },
    resetQuery(){
      this.queryParams={
        time:''
      }
      this.day10OutAndInCome()
    },
   
    day10OutAndInCome() {
      this.Time=[]
      this.Income=[]
      this.Expend=[]
      this.$nextTick(async () => {
        const {data:res} = await EchatsData({
          startTime:this.queryParams.time[0]||'',
          endTime :this.queryParams.time[1]||''
        })
      
        this.CountData=res.tjxx
        if (res.tjbb.length == 0) {
          const myData = this.$el.querySelector("#echarts")
          myData.style.display = "none"
          return
        }
        res.tjbb.map((item, index) => {
          this.Time.push(item.time)
          this.Income.push(item.sr)
          this.Expend.push(item.zc)
        });
        this.Monthlength=res.tjbb.length
        const option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
          },
          legend: {
            data: ["支出", "收入"],
          },
          yAxis: {
            type: "value",
            name: "单位/元",
            axisLabel: {
              formatter: '{value} 元',
            },
          },
          xAxis: [
            {
              name: "日期",
              type: "category",
              data: this.Time,
            },
          ],
          series: [
            {
              name: "支出",
              type: "bar",
              barWidth: "15%",
              tooltip: {
                valueFormatter: function (value) {
                  return value + "元"
                },
              },
              data: this.Expend,
            },
            {
              name: "收入",
              type: "bar",
              barWidth: "15%",

              tooltip: {
                valueFormatter: function (value) {
                  return value + "元"
                },
              },
              data: this.Income,
            },
          ],
        };
        this.mobileLine.setOption(option)
      });
    },
    initMobileLine() {
      //初始化图表
      this.mobileLine = echarts.init(document.getElementById("echart"))
    
      window.onresize = () => {
        this.mobileLine.resize()
      };
    },

    
    
  },
  // 图标初始化
  mounted() {
   
    let chart = elementResizeDetectorMaker()
    let that = this
    // chart-box为图表的父级div，通过监听父级div的宽度设置watch变量的值
    chart.listenTo(
      document.getElementsByClassName("echarts-container")[0],
      () => {
        that.$nextTick(() => {
          // 使echarts尺寸重置
          if (that.mobileLine) that.mobileLine.resize()
        });
      }
    );
    this.initMobileLine()
    this.day10OutAndInCome()

  },
};
</script>

<style scoped lang="scss">
.White{
  color:#fff;
}
#echart{
  height: 22.5rem;
  
}
::v-deep .el-card {
  border-radius: 1.25rem;
}
::v-deep .el-card__header {
  background-color: #409eff;
}
::v-deep .el-card__body {
  padding: 0px 0.75rem 0px 0.75rem;
}
.clearfix {
  display: flex;
  .recharge {
    flex: right;
    margin-left: 65%;
    ::v-deep .el-button--text {
      color: #fff;
      margin-top: -1.25rem;
    }
  }
}
</style>

