<template>
    <div>
        <el-dialog
        title="用户审核"
        :visible.sync="handleExameModel"
        width="34rem"
        append-to-body
      >
      <el-form ref="form" :model="form" :rules="rules" label-width="8rem">
        <el-form-item label="是否通过">
          <el-radio-group v-model="labels">
            <el-radio :label="0">通过</el-radio>
            <el-radio :label="1">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核驳回原因" prop="lysj" v-if="labels">
          <el-input
            v-model="form.lysj"
            type="textarea"
            :style="{ width: '100%' }"
            :autosize="{ minRows: 10, maxRows: 10 }"
            placeholder="请输入审核驳回原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormyx">确 定</el-button>
        <el-button @click="handleExameModel = false">取 消</el-button>
      </div>
    </el-dialog>
    </div>
</template>
<script>
export default {
    name: "UserExame.vue",
    props:{
        handleExameModel:{type:Boolean},
        form:{type:Object},
        rules:{type:Object}
    },
    data() {
      return {
        labels:0
      }
    },
    methods:{
        submitFormyx(){
            this.$emit('submitFormyx',this.$refs['form'],this.form,this.labels)
        }
    }
}
</script>