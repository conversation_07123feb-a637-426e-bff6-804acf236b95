<template>
    <div>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基础信息</span>
          <el-button
            style="float: right; padding: 0.1875rem 0 0"
            type="text"
            @click="back"
            >返回列表</el-button
          >
        </div>
        <!--    找保安-->
        <el-descriptions
          style="margin-top: 0px"
          :column="4"
          size="medium"
          border
          v-if="type ==1"
        >
          <el-descriptions-item
            label="政企客户名称"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.qymc ? userDetails.qymc : "" }}
          </el-descriptions-item> <el-descriptions-item
            label="负责人姓名"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.fzrMc ? userDetails.fzrMc : "" }}
          </el-descriptions-item>

          <el-descriptions-item
            label="联系电话"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.fzrDh ? userDetails.fzrDh : "" }}
          </el-descriptions-item>
  
          <el-descriptions-item
            label="邮箱"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.dzyx ? userDetails.dzyx : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="行业类别"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.hylb ? userDetails.hylb : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="组织机构号码"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{  userDetails.qydm ? userDetails.qydm : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="对公结算账户"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{userDetails.qyzh ? userDetails.qyzh : "" }}
          </el-descriptions-item>
         
          <el-descriptions-item
            label="状态"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.qyzt == 0 ?
             "通过" : userDetails.qyzt == 1 ? 
             "未提交" :userDetails.qyzt == 2 ? 
              "已提交":userDetails.qyzt == 3 ?
            '审核失败' :'' }}
          </el-descriptions-item>
          <el-descriptions-item
            label="审核时间"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.passTime ? userDetails.passTime : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="审核用户"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
          <el-button 
          type="primary"  
          size="small" 
          :disabled="['3','2'].includes(userDetails.qyzt)?false:true"
          @click="CompanyExame">
          {{['3','2'].includes(userDetails.qyzt)?"待审核":'已审核'}}
            <i class="el-icon-s-check el-icon--right"></i>
          </el-button>
         
          </el-descriptions-item>
         <!--  <el-descriptions-item
            label="备注"
            :span="2"
            :label-style="{ width: '10%' }"
          >
            {{ userDetails.bz ? userDetails.bz : "" }}
          </el-descriptions-item> -->
        </el-descriptions>
        <!--  找工作  -->
        <el-descriptions
          style="margin-top: 0px"
          :column="4"
          size="medium"
          border
          v-if="type!=1"
        >
          <el-descriptions-item
            label="姓名"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.xm || "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="创建时间"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.createTime || "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="联系电话"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.sjh ? userDetails.sjh : "" }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="推荐人手机号" :span="2" :label-style="{ width: '10%' }">
          {{ userDetails.refereePhone ? userDetails.refereePhone : '无' }}
        </el-descriptions-item> -->
          <el-descriptions-item
            label="性别"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.xb }}
          </el-descriptions-item>
          <el-descriptions-item
            label="年龄"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{calculateAge( userDetails.sfzh) }}
          </el-descriptions-item>
          <el-descriptions-item
            label="审核用户"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
          <el-button 
          type="primary"  
          size="small" 
          :disabled="['1','2'].includes(userDetails.shzt)?false:true"
          @click="ExameUser">
              {{['1','2'].includes(userDetails.shzt)?"待审核":'已审核'}}
            <i class="el-icon-s-check el-icon--right"></i>
          </el-button>
         
          </el-descriptions-item>
         
          <el-descriptions-item
            label="当前状态"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.dqzt==1?
            '找保安':userDetails.dqzt==2?
            '找工作':'找工作（队长）' }}
          </el-descriptions-item>
          <!-- <el-descriptions-item
            label="从事行业"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.cshy ? userDetails.cshy : "" }}
          </el-descriptions-item> -->
  
         <!--  <el-descriptions-item
            label="工作职位"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.gzzw ? userDetails.gzzw : "" }}
          </el-descriptions-item> -->
          <el-descriptions-item
            label="身高"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.sg ? userDetails.sg  : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="体重"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.tz ? userDetails.tz  : "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="会外语"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.wysp || "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="开户行"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.khh||"" }}
          </el-descriptions-item>
        
          <el-descriptions-item
            label="银行卡号"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.yhh||"" }}
          </el-descriptions-item>
        
          <el-descriptions-item
            label="退伍军人"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.sfjr==0 ? '是' : "否" }}
          </el-descriptions-item>
         
         <!--  <el-descriptions-item
            label="成长值"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ InfoContent.growthValue ||"0" }}分
          </el-descriptions-item>
          <el-descriptions-item
            label="积分"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ InfoContent.accumulatePoints ||"0" }}分
            <el-tag
                size="mini"
                style="margin-left: 1%"
                @click="PointsDetails()"
                >积分明细</el-tag
              >
          </el-descriptions-item>
          <el-descriptions-item
            label="信誉分"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ InfoContent.creditScore ||"0" }}分
          </el-descriptions-item>
          <el-descriptions-item
            label="个人标签"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.userTag ? userDetails.userTag : "" }}
          </el-descriptions-item> -->
         <!--  <el-descriptions-item
            label="审核状态"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{  userDetails.shzt == 0 ? "审核通过" :'未审核' }}
            <el-button type="primary" size='mini'  @click="getExame()" style='margin-left:10%;'  v-if="status != 0 &&status != 4">
              审核
            </el-button>
          </el-descriptions-item> -->
         <!--  <el-descriptions-item
            label="审核时间"
            :label-style="{ width: '10%' }"
            :content-style="{ width: '15%' }"
          >
            {{ userDetails.auditTime || "" }}
          </el-descriptions-item> -->
          <el-descriptions-item
            label="身份证地址"
            :span="2"
            :label-style="{ width: '10%' }"
          >
            {{ userDetails.sfzdz || "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="现地址"
            :span="2"
            :label-style="{ width: '10%' }"
          >
            {{ userDetails.xzz || "" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="身份证号"
            :span="2"
            :label-style="{ width: '10%' }"
          >
            {{ userDetails.sfzh || "" }}
          </el-descriptions-item>
     
        </el-descriptions>
      </el-card>
      <el-card
        class="box-card"
        header="证照信息"
        style="margin-top: 0.625rem"
        v-if="type == 1"
      >
        <div class="block">
          <span class="demonstration">营业执照</span>
          <el-image
            :src="userDetails.yyzzdz"
            :preview-src-list="srcListyyzzdz"
            style="width: 100%; margin-top: 0.625rem"
            fit="cover"
          >
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
       
      </el-card>
      <el-card
        class="box-card"
        header="证照信息"
        style="margin-top: 0.625rem"
        v-if="type!=1"
      >
        <div class="block">
          <span class="demonstration">形象照</span>
          <el-image
            :src="userDetails.xxz"
            style="width: 100%; margin-top: 0.625rem"
            :preview-src-list="srcList1"
            fit="cover"
          >
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
        <div class="block">
          <span class="demonstration">身份证正面</span>
          <el-image
            :src="userDetails.sfzzm"
            :preview-src-list="srcList2"
            style="width: 100%; margin-top: 0.625rem"
            fit="cover"
          >
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
  
        <div class="block">
          <span class="demonstration">身份证反面</span>
          <el-image
            :src="userDetails.sfzfm"
            :preview-src-list="srcList3"
            style="width: 100%; margin-top: 0.625rem"
            fit="cover"
          >
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
        <!-- <div class="block">
        <span class="demonstration">银行卡正面</span>
        <el-image
          :src="userDetails.yhkUrl"
          style="width: 100%; margin-top: 0.625rem"
          fit="cover"
        >
          <div slot="placeholder" class="image-slot">加载中<span class="dot">...</span></div>
        </el-image>
      </div> -->
        <div class="block">
          <span class="demonstration"
            >技能证书
            <!--              <span style="margin-left: 20px">{{ srcList5.length }}张</span>-->
          </span>
          <el-image
            :src="src5"
            :preview-src-list="srcList5"
            style="width: 100%; margin-right: 0px; margin-top: 0.625rem"
            fit="cover"
          >
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
      </el-card>
       <!-- 审核原因 -->
      <UserExame 
      :handleExameModel="handleExameModel" 
      :form="form" 
      :rules="rules"
      @submitFormyx="submitFormyx"></UserExame>
    </div>
  </template>
  
  <script>
  import {
    getXcxuser,
    getQyXx,
  } from "@/api/system/userDetail"
  import {listWxaExame,listWxaExameqy} from "@/api/system/wxa";
  import UserExame from './UserExame.vue'
  export default {
    name: "index.vue",
    inject: ["reload"],
    components:{UserExame},
    data() {
      return {
        showsh:false,
        label:0,
        InfoContent:{},
        handleExameModel:false,
        userDetails: {},
        srcListyyzzdz: [],
      
        srcList1: [],
        srcList2: [],
        srcList3: [],
        srcList5: [],
        src5: "",
        src1: "",
        src2: "",
        src3: "",
        src4: "",
        form:{
          lysj:''
        },
        flag:1,
        userId: this.$route.query.id,
        type: this.$route.query.type,
       /*  sf: this.$route.query.sf,
        userType: this.$route.query.userType,
        openid:this.$route.query.openid, */
       
         res: "",
         rules: {
          lysj:[  {required: true,  message: '回复内容不能为空', trigger: 'blur'}],
         
        },
      };
    },
    created() {
      this.getDeatil()
      this.getDeatilS()
    },
    activated() {
      this.getDeatil()
    },
    methods: {
      /* 企业信息 */
        async getDeatilS(){
          if(this.$route.query.type==1){
            let res = await getQyXx({userId:this.userId})
             this.userDetails = res.data
           
          }
            
        },
        /* 个人信息 */
      async getDeatil() {
        if(this.$route.query.type==2){
          let res = await getXcxuser({id:this.userId})
          this.userDetails = res.data
          console.log(res.data)
          this.srcListyyzzdz.push(this.userDetails.yyzzdz)
        
          this.srcList1.push(this.userDetails.xxz)
          this.srcList2.push(this.userDetails.sfzzm)
          this.srcList3.push(this.userDetails.sfzfm)
          this.src5 = this.userDetails.jnzs?this.userDetails.jn.split(",")[0]:''
          this.srcList5 = this.userDetails.jnzs?this.userDetails.jn.split(","):[]
        }
        
      },
      getExame() {
        // 用户审核
         this.showsh=true
      },
    calculateAge(idCard) {
      if (idCard.length!== 18) {
          return null;
      }
      // 提取出生日期
      const birthDateStr = idCard.slice(6, 14);
      const birthDate = new Date(birthDateStr.slice(0, 4), birthDateStr.slice(4, 6) - 1, birthDateStr.slice(6, 8));
      const currentDate = new Date();
      let age = currentDate.getFullYear() - birthDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      const birthMonth = birthDate.getMonth();
      if (currentMonth < birthMonth) {
          age--;
      } else if (currentMonth === birthMonth) {
          const currentDay = currentDate.getDate();
          const birthDay = birthDate.getDate();
          if (currentDay < birthDay) {
              age--;
          }
      }
      return age;
},
    // 查看明细
      PointsDetails(){
        this.$router.push({
          path:'/vipMx',
          query:{
            openid:this.openid
          }
        })
      },
      // 用户审核
      ExameUser(){
        this.flag=2
        this.handleExameModel=true
        
      },
      // 企业审核
      CompanyExame(){
        this.flag=1
        this.handleExameModel=true
      },
      // 审核提交
      submitFormyx(Form,form,labels){
        Form.validate(async(valid) => {
          if (valid) {
            if(this.flag==1){
            const res = await listWxaExameqy({
            userId:this.userId,
            shzt:labels==0?0:3,
            sbxx:form.lysj
          });
            this.handleExameModel = false;
            this.$modal.msgSuccess(`${res.msg}`);
            this.reload();
            this.resetQuery()
           form.lysj = "";
        }else{
          const res = await listWxaExame({
            userId:this.userId,
            shzt:labels==0?0:1,
            sbxx: form.lysj
          });
          this.handleExameModel = false;
          this.$modal.msgSuccess(`${res.msg}`);
          this.reload();
          this.resetQuery()
          form.lysj = "";
    }   
          } 
        });
        
      },
      back() {
        this.$store.state.tagsView.visitedViews.splice(
          this.$store.state.tagsView.visitedViews.findIndex(
            (item) => item.path === this.$route.path
          ),
          1
        )
        this.$router.push(
          this.$store.state.tagsView.visitedViews[
            this.$store.state.tagsView.visitedViews.length - 1
          ].path
        )
        this.$destroy()
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .block {
    display: inline-block;
    margin-right: 15px;
    width: 19%;
  }
  .demonstration {
    display: block;
    text-align: center;
  }
  </style>
  