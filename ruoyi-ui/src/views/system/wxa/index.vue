<template>
  <div class="app-container">
    <el-form :model="queryParams"
    ref="queryForm"
    size="small"
    :inline="true"
    v-show="showSearch"
    label-width="99px">
      <el-form-item label="用户姓名" prop="xm">
        <el-input
          v-model="queryParams.xm"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="sjh">
        <el-input
          v-model="queryParams.sjh"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="shzt">
        <el-select v-model="queryParams.shzt" placeholder="请选择审核状态" clearable>
          <el-option
            v-for="dict in dict.type.user_wxa_shzt"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="实名认证状态" prop="smrz">
        <el-select v-model="queryParams.smrz" placeholder="请选择实名认证状态" clearable>
          <el-option
            v-for="dict in dict.type.user_wxa_smrz"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="身份" prop="sf">
        <el-select v-model="queryParams.sf" placeholder="请选择身份" clearable>
          <el-option
            v-for="dict in dict.type.user_wxa_sf"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前状态" prop="dqzt">
        <el-select v-model="queryParams.dqzt" placeholder="请选择当前状态" clearable>
          <el-option
            v-for="dict in dict.type.user_wxa_dqzt"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
         <div class="block">
          <el-date-picker
            v-model="queryParams.time"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :unlink-panels="true"
            :default-time="['00:00:00', '23:59:59']">
          >
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:wxa:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wxaList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" width="55"/> -->
      <el-table-column label="用户姓名" align="center" prop="xm" />
      <el-table-column label="用户形象照" align="center" prop="xxz" >
        <template slot-scope="scope" >
           <el-image
           @click="preview(scope.row.xxz)"
            style="width: 50px; height: 50px;border-radius:50%;"
            :src="scope.row.xxz"
            :preview-src-list="srcList">
          </el-image>

        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" /> 
      <el-table-column label="身份证号" align="center" prop="sfzh" width="165"/>
      <el-table-column label="后台审核状态" align="center" prop="shzt">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_wxa_shzt" :value="scope.row.shzt"/>
        </template>
      </el-table-column>
      <el-table-column label="是否实名认证" align="center" prop="smrz">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_wxa_smrz" :value="scope.row.smrz"/>
        </template>
      </el-table-column>

      <el-table-column label="用户手机号" align="center" prop="sjh" />

      <el-table-column label="身份" align="center" prop="sf">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_wxa_sf" :value="scope.row.sf"/>
        </template>
      </el-table-column>
      <el-table-column label="当前状态" align="center" prop="dqzt">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_wxa_dqzt" :value="scope.row.dqzt"/>
        </template>
      </el-table-column>
      <el-table-column label="企业状态" align="center" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qy_shzt" :value="scope.row.qyzt"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleExame(scope.row)"
            v-if="Number(scope.row.shzt)==2||Number(scope.row.shzt)==1"
            v-hasPermi="['system:wxa:yhsh']"
          >用户审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleExameqy(scope.row)"
            v-if="Number(scope.row.qyzt)==2||Number(scope.row.qyzt)==3"
            v-hasPermi="['system:wxa:qyrz']"
          >企业审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row,2)" 
            v-hasPermi="['system:wxa:selectXq']"
          >个人详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.dqzt==1"
            @click="handleDetail(scope.row,1)"
            v-hasPermi="['system:wxa:qyxx']"
          >企业详情</el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.zsyg==1"
            @click="upUserOnclick(scope.row)"
            v-hasPermi="['system:wxa:zsyg']"
          >用户升级</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.zsyg==0"
            @click="CancelUserOnclick(scope.row)"
            v-hasPermi="['system:wxa:zsyg']"
          >用户取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改微信小程序用户信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="身份证反面照，通常存储照片的URL" prop="sfzfm">
          <el-input v-model="form.sfzfm" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="后台审核状态" prop="shzt">
          <el-select v-model="form.shzt" placeholder="请选择后台审核状态">
            <el-option
              v-for="dict in dict.type.user_wxa_shzt"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否实名认证" prop="smrz">
          <el-select v-model="form.smrz" placeholder="请选择是否实名认证">
            <el-option
              v-for="dict in dict.type.user_wxa_smrz"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="现住址" prop="xzz">
          <el-input v-model="form.xzz" placeholder="请输入现住址" />
        </el-form-item>
        <el-form-item label="开户行" prop="khh">
          <el-input v-model="form.khh" placeholder="请输入开户行" />
        </el-form-item>
        <el-form-item label="银行卡号" prop="yhh">
          <el-input v-model="form.yhh" placeholder="请输入银行卡号" />
        </el-form-item>
        <el-form-item label="技能证书url，多个逗号分割" prop="jn">
          <el-input v-model="form.jn" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="用户身高" prop="sg">
          <el-input v-model="form.sg" placeholder="请输入用户身高" />
        </el-form-item>
        <el-form-item label="用户体重" prop="tz">
          <el-input v-model="form.tz" placeholder="请输入用户体重" />
        </el-form-item>
        <el-form-item label="是否退伍军人" prop="sfjr">
          <el-select v-model="form.sfjr" placeholder="请选择是否退伍军人">
            <el-option
              v-for="dict in dict.type.user_wxa_sfjr"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="外语水平" prop="wysp">
          <el-input v-model="form.wysp" placeholder="请输入外语水平" />
        </el-form-item>
        <el-form-item label="用户性别" prop="xb">
          <el-select v-model="form.xb" placeholder="请选择用户性别">
            <el-option
              v-for="dict in dict.type.user_wxa_xb"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户手机号" prop="sjh">
          <el-input v-model="form.sjh" placeholder="请输入用户手机号" />
        </el-form-item>
        <el-form-item label="身份证地址" prop="sfzdz">
          <el-input v-model="form.sfzdz" placeholder="请输入身份证地址" />
        </el-form-item>
        <el-form-item label="推广二维码的URL" prop="wxaCodeUrl">
          <el-input v-model="form.wxaCodeUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="身份" prop="sf">
          <el-select v-model="form.sf" placeholder="请选择身份">
            <el-option
              v-for="dict in dict.type.user_wxa_sf"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否正式员工" prop="zsyg">
          <el-select v-model="form.zsyg" placeholder="请选择是否正式员工 ">
            <el-option
              v-for="dict in dict.type.user_wxa_zsyg"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前状态  " prop="dqzt">
          <el-select v-model="form.dqzt" placeholder="请选择当前状态 ">
            <el-option
              v-for="dict in dict.type.user_wxa_dqzt"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 审核订单弹窗 -->
    <el-dialog
      title="用户审核"
      :visible.sync="handleExameModel"
      width="34rem"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="8rem">
        <el-form-item label="是否通过">
          <el-radio-group v-model="labels">
            <el-radio :label="0">通过</el-radio>
            <el-radio :label="1">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核驳回原因" prop="lysj" v-if="labels">
          <el-input
            v-model="form.lysj"
            type="textarea"
            :style="{ width: '100%' }"
            :autosize="{ minRows: 10, maxRows: 10 }"
            placeholder="请输入审核驳回原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormyx">确 定</el-button>
        <el-button @click="handleExameModel = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWxa, getWxa, delWxa, addWxa,
updateWxa,listWxaExame,listWxaExameqy ,upUser} from "@/api/system/wxa";
export default {
  name: "Wxa",
  inject: ["reload"],
  dicts: ['user_wxa_zsyg','user_qy_shzt', 'user_wxa_dqzt', 'user_wxa_xb', 'user_wxa_smrz', 'user_wxa_shzt', 'user_wxa_sf', 'user_wxa_sfjr'],
  data() {
    return {
      // 审核用户
      handleExameModel:false,
      handleExameVal:null,
      labels:0,
     //预览图片
      srcList: [ ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 微信小程序用户信息表格数据
      wxaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xm: null,
        shzt: null,
        smrz: null,
        sf: null,
        dqzt: null,
        sjh:null,
        time:[],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      flag:0
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信小程序用户信息列表 */
    getList() {
      this.loading = true
      let newdata={
        ...this.queryParams,
        bigDate:this.queryParams.time[0],
        endDate:this.queryParams.time[1]
      }
      listWxa(newdata).then(response => {
        this.wxaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 图片预览
    preview(val){
      this.srcList.push(val)
    },
     // 审核用户
    handleExame(val){
      this.handleExameModel=true
      this.handleExameVal=val

    },
    // 企业审核
    handleExameqy(val){
       this.flag=1
       this.handleExameModel=true
       this.handleExameVal=val
    },
    /* 查看详情 */
    handleDetail(val,num){
      this.$router.push({path:'/userDetail',
      query:{id:val.id,
      type:num}
    })
    
    },

    // 升级用户
   async upUserOnclick(val){
    const res=await upUser({id:val.id,zsyg:0})
    this.getList()
    // this.reload();
    this.$modal.msgSuccess(`${res.msg}`)
    },
    // 取消升级用户
   async CancelUserOnclick(val){
      const res=await upUser({id:val.id,zsyg:1})
      this.getList()
      // this.reload();
      this.$modal.msgSuccess(`${res.msg}`)
    },
  // 用户审核
    async submitFormyx() {
    //  shzt 0通过  1不通过
    if(this.flag==1){
    const res = await listWxaExameqy({
        userId:this.handleExameVal.id,
        shzt:this.labels==0?0:3,
        sbxx: this.form.lysj
      });
      this.handleExameModel = false;
      this.$modal.msgSuccess(`${res.msg}`);
      this.reload();
      this.resetQuery()
      this.form.lysj = "";
    }else{
      const res = await listWxaExame({
        userId:this.handleExameVal.id,
        shzt:this.labels==0?0:1,
        sbxx: this.form.lysj
      });
       this.handleExameModel = false;
      this.$modal.msgSuccess(`${res.msg}`);
      this.reload();
      this.resetQuery()
      this.form.lysj = "";
    }

    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        openid: null,
        unionid: null,
        xm: null,
        sfzh: null,
        xxz: null,
        sfzzm: null,
        sfzfm: null,
        shzt: null,
        smrz: null,
        xzz: null,
        khh: null,
        yhh: null,
        jn: null,
        sg: null,
        tz: null,
        sfjr: null,
        wysp: null,
        xb: null,
        sjh: null,
        sfzdz: null,
        wxaCodeUrl: null,
        sf: null,
        zsyg: null,
        dqzt: null,
        createTime: null,
        updateTime: null,
        delFlag: null,
         lysj:null,
         sjh:null,
         time:[]
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.time=''
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加微信小程序用户信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWxa(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改微信小程序用户信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWxa(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWxa(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除微信小程序用户信息编号为"' + ids + '"的数据项？').then(function() {
        return delWxa(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/wxa/export', {
        ...this.queryParams,
        bigDate:this.queryParams.time[0],
        endDate:this.queryParams.time[1]
      }, `wxa_${new Date().getTime()}.zip`)
    }
  }
};
</script>
