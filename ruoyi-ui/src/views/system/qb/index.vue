<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="姓名" prop="xm">
        <el-input
          v-model="queryParams.xm"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="余额" prop="ye">
        <el-input
          v-model="queryParams.ye"
          placeholder="请输入余额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="提现状态" prop="txzt">
        <el-select v-model="queryParams.txzt" placeholder="请选择提现状态" clearable>
          <el-option
            v-for="dict in dict.type.user_qb_txzt"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
     <!--  <el-form-item label="修改金额" prop="xgje">
        <el-input
          v-model="queryParams.xgje"
          placeholder="请输入修改金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCjsj"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:qb:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:qb:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:qb:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:qb:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="qbList" @selection-change="handleSelectionChange">
      <!-- <el-table-column label="id" align="center" prop="userId" /> -->
      <el-table-column label="姓名" align="center" prop="xm" />
      <el-table-column label="余额" align="center" prop="ye" />
      <el-table-column label="奖励金余额" align="center" prop="hbye" />
      <el-table-column label="提现状态" align="center" prop="txzt">

        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qb_txzt" :value="scope.row.txzt"/>
        </template>
      </el-table-column>
      <el-table-column label="修改金额" align="center" prop="xgje" />
      <el-table-column label="创建时间" align="center" prop="cjsj" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.cjsj }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDeatil(scope.row)"
            v-hasPermi="['system:qb:edit']"
          >查看明细</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:qb:remove']"
          >余额变更</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小程序用户钱包对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="余额" prop="ye">
          <el-input v-model="form.ye" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="红包余额" prop="hbye">
          <el-input v-model="form.hbye" placeholder="请输入红包余额" />
        </el-form-item>
        <!-- <el-form-item label="提现状态" prop="txzt">
          <el-select v-model="form.txzt" placeholder="请选择提现状态">
            <el-option
              v-for="dict in dict.type.user_qb_txzt"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注" prop="bz">
          <el-input v-model="form.bz" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQb, getQb, delQb, addQb, updateQb } from "@/api/system/qb";

export default {
  name: "Qb",
  dicts: ['user_qb_txzt', 'user_qb_lzlx', 'user_qb_sf'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小程序用户钱包表格数据
      qbList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCjsj: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        ye: null,
        hbye: null,
        txzt: null,
        xgje: null,
        cjsj: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询小程序用户钱包列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCjsj && '' != this.daterangeCjsj) {
        this.queryParams.params["beginCjsj"] = this.daterangeCjsj[0];
        this.queryParams.params["endCjsj"] = this.daterangeCjsj[1];
      }
      listQb(this.queryParams).then(response => {
        this.qbList = response.rows;
        this.total = response.total;
        this.loading = false;
      });

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        ye: null,
        hbye: null,
        txzt: null,
        bz: null,
        szlx: null,
        xgje: null,
        lzlx: null,
        sf: null,
        ddbh: null,
        cjsj: null,
        xgsj: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    // 查看明细
    handleDeatil(val){

      this.$router.push({
        path:'/qbMx',
        query:{id:val.userId}
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCjsj = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加小程序用户钱包";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getQb(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改小程序用户钱包";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateQb(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQb(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除小程序用户钱包编号为"' + ids + '"的数据项？').then(function() {
        return delQb(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/qb/export', {
        ...this.queryParams
      }, `qb_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
