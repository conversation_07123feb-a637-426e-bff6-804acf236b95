<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:dd:exportGzlb']"
          >导出</el-button
        >
      </el-col>
       <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          v-if='total==1&&roles=="FD"'
          @click="Gzsend"
          v-hasPermi="['system:gzlb:tyff']"
          >发放劳务费</el-button>
          <!-- &&roles=="FD" -->
      </el-col>
      
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>
     <el-row :gutter="10" class="mb8" style="margin-left:0px;">
      <el-col :span="1.5">
        <el-button
          type="text"
          size="medium"
          v-hasPermi="['system:gzlb:export']"
          >部门: 临保</el-button >
      </el-col>
   
       <el-col :span="1.5">
        <el-button
          type="text"
          size="medium"
          v-hasPermi="['system:gzlb:export']"
          >项目名称: {{title||""}}</el-button >
      </el-col>
    </el-row>
    <el-table
      v-loading="loading"
      :data="gzlbList"
      @selection-change="handleSelectionChange"
      :cell-style="tableRowStyle"
    >
      <!-- <el-table-column label="订单编号" align="center" prop="ddId" /> -->
      <el-table-column label="序号" align="center" prop="xh" />
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="基本劳务费" align="center" prop="sfgz" />
      <el-table-column label="奖金1" align="center" prop="jj1" />
      <el-table-column label="奖金2" align="center" prop="jj2" />
      <el-table-column label="vip等级奖励" align="center" prop="vipAward" />
      <el-table-column label="补贴" align="center" prop="bt" />
      <el-table-column label="违纪扣款" align="center" prop="wjkk" />
      <el-table-column label="实发合计" align="center" prop="sfhj" />
      <el-table-column label="班数" align="center" prop="bs" />
      <el-table-column label="小时" align="center" prop="zgs" />
      <el-table-column label="身份证号码" align="center" prop="sfzh" width='200'/>
      <el-table-column label="手机号码" align="center" prop="phone" /> 
    </el-table>
     <el-dialog title="劳务费发放" :visible.sync="open" width="31.25rem" append-to-body>
      <el-form ref="gzform" :model="gzform"  label-width="6.25rem">
        <el-form-item label="劳务费发放状态" >
          <el-radio-group  v-model="gzform.gzffzt">
           <el-radio :label="4">发放</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormgz">确 定</el-button>
        <el-button @click="open=false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDd,exportDd } from "@/api/system/gzlb";

export default {
  name: "Gzlb",
  // dicts: ["sys_gzffzt", "sys_gzhdzt"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 劳务费列表格数据
      gzlbList: [],
      // 弹出层标题
      title: this.$route.query.xmmc,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        ddid: this.$route.query.id,
      },
       gzform:{
        gzffzt:4,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      time:'',
      roles:'',
      status:this.$route.query.status
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询劳务费列列表 */
    
    getList() {
     
      this.roles=this.$store.state.user.name
      this.loading = true;
      listDd(this.queryParams).then((response) => {
        console.log(response,222)
        this.gzlbList =response.data;
        this.loading = false;
        // this.title=response.data.xmmc
        // this.time=response.data.zjlshsj
        this.gzlbList.map(item=>{
          if(this.gzlbList[0].gzffzt==3&&this.gzlbList[0].zjlsh==0){
            this.total=1
            
          }
          if(this.gzlbList[0].gzffzt==4){
            this.total=2
          }
        })
      });
    },
    tableRowStyle({ row, column, rowIndex, columnIndex }){
         if(row.xh==='总计'){
            return 'color:red; font-weight:900;!important;'
         }
    },
    // 劳务费发放
    Gzsend(){
          this.open=true
    },
    async submitFormgz(){
        this.loading = true;
        this.open=false
        let res=await GzSend(this.queryParams)
        this.$modal.msgSuccess('劳务费发放成功')
        this.loading = false;
        this.total=1
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ddId: null,
        gzhdzt: null,
        gzffzt: null,
        yx: null,
        gsf: null,
        zgs: null,
        yfgz: null,
        sfgz: null,
        xmmc: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加劳务费列";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGzlb(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改劳务费列";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateGzlb(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGzlb(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除劳务费列编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGzlb(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/system/dd/exportGzlb",
        {
          ...this.queryParams,
        },
        `gzlb_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss">
</style>