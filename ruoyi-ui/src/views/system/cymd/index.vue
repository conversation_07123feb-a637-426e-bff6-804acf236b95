<template>
    <div class="app-container">
      <el-form
        v-show="showSearch"
        ref="queryForm"
        :model="queryParams"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model="queryParams.xm"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="接单状态" prop="sfjd">
          <el-select v-model="queryParams.sfjd" placeholder="请选择接单状态" clearable>
            <el-option
              v-for="dict in dict.type.sys_dd_sfjd"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >
          搜索
        </el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >
          重置
        </el-button>
          <keep-alive>
          <!--   <el-button
              icon="el-icon-zoom-in"
              size="mini"
              type="warning"
              @click="addUser"
            >
            添加接单人员
          </el-button> -->
          </keep-alive>
        </el-form-item>
        <el-button
          style="float: right; padding: 0px 0"
          type="text"
          @click="back"
        >
        返回列表
      </el-button>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system/cymd/export']"
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar
          :show-search.sync="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-table v-loading="loading" :data="cymdList">
        <!-- <el-table-column label="序号" align="center" prop="id" /> -->
        <el-table-column label="名字" align="center" prop="xm" width="95"/>
        <el-table-column label="手机号" align="center" prop="sjh"  />
        <el-table-column label="接单时间" align="center" prop="cjsj"/>
        <el-table-column label="日薪" align="center" prop="rx"  />
        <el-table-column label="规定上班时间" align="center" prop="kssj" />
        <el-table-column label="规定下班时间" align="center" prop="jssj" />
        <el-table-column label="实际上班时间" align="center" prop="sbdksj"  />
        <el-table-column label="实际下班时间" align="center" prop="xbdksj"  />
        <!-- <el-table-column label="等级" align="center" prop="rank"  width="55">
          <template  slot-scope="scope">
           VIP {{ scope.row.rank }}
          </template>
        </el-table-column>
        <el-table-column label="是否接单" align="center" prop="sfjd">
          <template slot-scope="scope">
            {{ scope.row.sfjd==1?"已接单":scope.row.sfjd==3?"已取消":'' }}
          </template>
        </el-table-column>
        <el-table-column label="角色" align="center" prop="sfdz"  width="55">
          <template slot-scope="scope">
            {{ scope.row.sfdz == 0 ? "队长" : "成员" }}
           
          </template>
        </el-table-column>
        <el-table-column label="出勤时间" align="center" prop="kssj" />
        <el-table-column label="结束时间" align="center" prop="jssj" /> -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <!-- <el-button
              v-hasPermi="['system:gzlb:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="signup(scope.row)"
            >
            签到详情
          </el-button> -->
          <el-button
              v-hasPermi="['system:cymd:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              :disabled="!scope.row.sfxg"
              @click="Edit(scope.row)"
            >
            编辑
          </el-button>
            <el-button
              v-if="scope.row.sfqx==false&&scope.row.sfjd!=3"
              v-hasPermi="['system:gzlb:edit']"
              icon="el-icon-delete"
              size="mini"
              type="text"
              @click="delUser(scope.row)"
            >
            取消出勤
          </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
     <!-- 取消原因 -->
      <el-dialog
        title="是否取消"
        :visible.sync="showdel"
        width="500px"
        append-to-body
      >
      <el-form
          ref="form"
          :model="form"
          label-width="120px"
          style="margin-top: -25px"
        >
      <el-form-item  label="取消原因">
           <el-input type='text'   v-model="form.qxyy"  placeholder="请输入原因"></el-input>
        </el-form-item>
      </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showUserdel">确 定</el-button>
          <el-button @click="showdel = false">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 添加参与名单 -->
      <el-dialog
        title="添加参与人员"
        :visible.sync="showUser"
        width="1200px"
        append-to-body
        :show-close="false"
      >
        <el-form
          ref="form"
          :model="queryform"
          size="small"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="姓名">
            <el-input
              v-model="queryform.userName"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQueryform"
            />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model="queryform.phonenumber"
              placeholder="请输入手机号"
              clearable
              @keyup.enter.native="handleQueryform"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryform"
            >搜索</el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQueryform"
            >重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="userlist">
          <el-table-column label="名字" align="center" prop="xm" />
          <el-table-column label="性别" align="center" prop="sex" />
          <el-table-column label="年龄" align="center" prop="sr" />
          <el-table-column label="性别" align="center" prop="sex" />
          <el-table-column label="身高" align="center" prop="sg">
            <template slot-scope="scope">
              {{ scope.row.sg ? scope.row.sg + "cm" : "" }}
            </template>
          </el-table-column>
          <el-table-column label="手机号码" align="center" prop="phonenumber" />
      
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:gzlb:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="addtime(scope.row)"
              >选择出勤时间</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryform.pageNum"
          :limit.sync="queryform.pageSize"
          @pagination="getlistUser"
        />
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showUser = false">确 定</el-button>
          <el-button @click="showUser = false">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 添加时间段 -->
      <el-dialog
        :title="title"
        :visible.sync="showTime"
        width="1400px"
        append-to-body
        :show-close="false"
      >
        <el-table
          v-loading="loading"
          :data="getTimelist"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="selectEnable"
          />
          <el-table-column label="订单编号" align="center" prop="ddId" width="170"/>
          <el-table-column label="剩余名额" align="center" prop="syme" width="120"/>
          <el-table-column label="日期" align="center" prop="rq" >
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.rq"
                type="date"
                value-format="yyyy-MM-dd"
                >
              </el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="上班时间" align="center" prop="startTime" >
            <template slot-scope="scope">
              <el-time-select
              v-model="scope.row.startTime"
              value-format="HH:mm"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '24:30',
              }"
                >
              </el-time-select>
            </template>
          </el-table-column>
          <el-table-column label="下班时间" align="center" prop="endTime" >
            <template slot-scope="scope">
              <el-time-select
              v-model="scope.row.endTime"
              value-format="HH:mm"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '24:30',
              }"
                >
              </el-time-select>
            </template>
          </el-table-column>
          <el-table-column label="日薪" align="center" >
            <template slot-scope="scope">
                              <el-input v-model.number="scope.row.rx"  :min="0"  type="number">
                  <template slot="append">元</template>
                </el-input>
                          </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showTimeconfirm">确 定</el-button>
          <el-button @click="showTime = false">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 修改出勤时间 -->
      <el-dialog   
          title="修改出勤时间"
          width="550px"
          :visible.sync="showEdit"
          append-to-body
          :show-close="false"
          center>
          <el-form ref="form" :model="form" :rules="rules"  label-width="100px">
            <el-form-item  label="名字" >
           <el-input type='text'   v-model="getEditlist.userName"  style="width: 220px" :disabled="true" />
        </el-form-item>
          <el-form-item  label="出勤时间" prop="cqsj">
            <el-date-picker
              v-model="form.cqsj"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item  label="结束时间" prop="jssj">
            <el-date-picker
              v-model="form.jssj"
              value-format="yyyy-MM-dd HH:mm"
              type="datetime"
              placeholder="选择日期时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item  label="劳务费"  prop="gz">
           <el-input type='text'   v-model="form.gz"  style="width: 220px">
            <template slot="append">元</template></el-input>
        </el-form-item>
          </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showEditconfirm">确 定</el-button>
          <el-button @click="cancelEdit">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 审核备注 -->
    <div style="margin-top:20px;" class="shstep">
    <div class="kqstep" >
      <div  
      v-for="(item,index) in listProgress" 
      :key='index' 
      :class='item.shzt==2?"two":(item.shzt==3?"three":"one")' 
      @click="shSatus(index)">
        <div class="yuan" >{{index+1}}</div>
        <div class='name' >审批人{{item.shsx}}:</div>
        <div class="name1" >{{item.xm}}
            <span style="font-size:11px;">{{`(${item.spr_lx})`}}</span>
            <span> ---------></span>
        </div>
         <!-- <el-dialog title="考勤审核" :visible.sync="showkqbz" width="31.25rem" append-to-body >
          <span style="font-size:16px;margin-right:3px;">备注</span>{{kqlist[kqindex].auditRemark||"无"}}
         </el-dialog> -->
      </div>
    </div>
    </div>
    </div>
  </template>
  
  <script>
  import {
    listCymd,
    getCymd,
    getlistUser,
    getlistTime,
    getaddtime,
    getdelTime,
    getcaneltime,
    listCymdProgress
  } from '@/api/system/cymd'
  
  export default {
    name: 'Cymd',
    dicts: ['sys_dd_sfjd'],
    inject: ['reload'],
    data() {
      return {
        showdel: false,
        getEditlist:{},
        queryform: {
          xm: '',
          ddid: this.$route.query.id,
         /*  phonenumber: '', */
          pageNum: 1,
          pageSize: 10
        },
        title: '',
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 参与名单表格数据
        cymdList: [],
        // 弹出层标题
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          userName: null,
          ddid: this.$route.query.id,
          sfjd: ''
        },
        ddid: this.$route.query.id,
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          // cqsj:[{ required: true, message: '出勤时间不能为空', trigger: 'blur' }],
          // jssj:[{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
          // gz:[{ required: true, message: '劳务费不能为空', trigger: 'blur' }],
        },
        showUser: false,
        showTime: false,
        userlist: [],
        getTimelist: [],
        appid: '',
        timeArr: [],
        rx: '',
        row: {},
        resarry:[],
        showEdit:false,
        dkjl:'',
        listProgress:[],//审核流程
      }
    },
  
    activated() {
      this.getList()
    },
    created() {
      this.getList()
      this.getProgress()
      this.queryParams.sfjd = '1'
    },
    methods: {
      selectEnable(row, rowIndex) {
        // 审核通过禁用
        if (row.syme != 0) {
          return true
        }
      },
      // 审核步骤
     async getProgress(){
        const res=await listCymdProgress({ddid:this.$route.query.id})
        this.listProgress=res.rows
        console.log(res,111)
      },
      // 修改出勤时间
      Edit(row){
        this.getEditlist=row
        this.form={
          gz:this.getEditlist.gz,
          cqsj:this.getEditlist.cqsj,
          jssj:this.getEditlist.jssj,
        }
        this.showEdit=true
      },
      cancelEdit(){
        this.showEdit=false
      },
     async showEditconfirm(){
      if(this.form.cqsj==null){
        this.$modal.msgError('出勤时间不能为空')
        return 
      }
      if(this.form.jssj==null){
        this.$modal.msgError('结束时间不能为空')
        return 
      }
      let start=Number(this.form.cqsj.slice(14,20))
      let end=Number(this.form.jssj.slice(14,20))
      if(start%30 !==0){
        this.$modal.msgError('出勤时间必须为整点或者半点')
        return 
      }
      if(end%30!==0){
        this.$modal.msgError('结束时间必须为整点或者半点')
        return 
      }
      if(!this.form.gz){
        this.$modal.msgError('劳务费不能为空')
        return 
      }
      // this.$refs['form'].validate(async(valid) => {
      //  if (valid) {
        console.log(this.form.cqsj,this.form.jssj,this.form.gz)
      let res= await getcaneltime(
        {
          id:this.getEditlist.id,
          cqsj:this.form.cqsj,
          jssj:this.form.jssj,
          gz:this.form.gz
        })
      this.$modal.msgSuccess('修改成功！')
      this.showEdit=false
      this.getList()
    //   }
    // })
      },
      // 添加出勤人员
      async showTimeconfirm() {
        if(!(this.resarry.length>0)){
          this.$modal.msgError('请至少选择一个时间段')
          return
        }
        this.timeArr = []
        this.resarry.map((item, index) => {
          const start =item.rq + ' ' + item.startTime
          const end = item.rq + ' ' + item.endTime
          const rx = item.rx
          this.timeArr.push({
            cqsj: start,
            jssj: end,
            rx: rx
          })
        })
        // console.log(this.timeArr)
        const time = {
          ddid: this.$route.query.id,
          sysjs: this.timeArr,
          jdrOpenid: this.appid
        }
        const res = await getaddtime(time)
        this.$modal.msgSuccess('增加成功')
        this.reload()
        this.getList()
        this.showTime = false
        this.showUser = false
      },
      handleSelectionChange(e) {},
      delUser(row) {
        this.row = row
        this.showdel = true
      },
      async showUserdel() {
        const res = await getdelTime({ 
           qxrq: [this.row.cqsj.substring(0, 10)], 
           ddid: this.row.ddId, 
           jdrOpenid: this.row.jdrOpenid,
           qxyy:this.form.qxyy })
        this.showdel = false
        this.$modal.msgSuccess('取消成功')
        this.getList()
      },
      async addtime(row,index) {
        this.appid = row.openid
        const { data: res } = await getlistTime({
          ddid:this.$route.query.id,
          openid:row.openid
        })
        this.getTimelist = res.fwzq
        this.title = '添加服务周期'
        this.showTime = true
      },
      handleQueryform() {
        this.getlistUser()
      },
      resetQueryform() {
        this.queryform = {
          xm: '',
          phonenumber: '',
          ddId: this.$route.query.id
        }
        this.getlistUser()
      },
      addUser() {
        this.getlistUser()
        this.showUser = true
      },
      async getlistUser() {
        const res = await getlistUser(this.queryform)
        this.userlist = res.rows
        this.total = res.total
      },
      signup(row) {
        const id = row.ddId
        this.$router.push({
          // 核心语句
          name: 'qdry',
          query: {
            id: id,
            openid: row.jdrOpenid
          }
        })
      },
      /** 查询参与名单列表 */
      getList() {
        this.loading = true
        listCymd(this.queryParams).then((response) => {
          this.cymdList = response.rows
          this.total = response.total
          this.loading = false
        })
      },
      back() {
        this.$store.state.tagsView.visitedViews.splice(
          this.$store.state.tagsView.visitedViews.findIndex(
            (item) => item.path === this.$route.path
          ),
          1
        )
        this.$router.push(
          this.$store.state.tagsView.visitedViews[
            this.$store.state.tagsView.visitedViews.length - 1
          ].path
        )
        this.$destroy()
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          ddId: null,
          zw: null,
          jdsj: null,
          sfjd: null,
          yy: null,
          jdrs: null,
          cqsj: null,
          jssj: null,
          jdrOpenid: null,
          jssj:null,
          cqsj:null,
          gz:null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.resarry=selection
        this.ids = selection.map((item) => item.id)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset()
        this.open = true
        this.title = '添加参与名单'
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const id = row.id || this.ids
        getCymd(id).then((response) => {
          this.form = response.data
          this.open = true
          this.title = '修改参与名单'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            if (this.form.id != null) {
              updateCymd(this.form).then((response) => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              addCymd(this.form).then((response) => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids
        this.$modal
          .confirm('是否确认删除参与名单编号为"' + ids + '"的数据项？')
          .then(function() {
            return delCymd(ids)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {})
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download(
          'system/cymd/export',
          {
            ...this.queryParams
          },
          `cymd_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
  </script>
  <style lang="scss" scoped>
      .kqstep{
   display: flex;
   justify-content: center;
   flex-wrap: wrap;
   width: 100%;
   min-height: 180px;
   font-size: 17px;
   font-weight:600;
   .c1{//审核中
     display: flex;
     width: 280px;
    //  margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #ffbe3c !important;
        text-align: center;
        line-height: 20px;
        color: #ffbe3c;
    }
    .name{
        margin-left:5px;
        color: #ffbe3c;
    }
    .name1{
          position: relative;
           color: #ffbe3c;
         span{
           color: #ffbe3c;
         }
    }
}
  .three{
     display: flex;
     width: 280px;
      // margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #15d36a !important;
        text-align: center;
        line-height: 20px;
        color: #15d36a;
    }
    .name{
        margin-left:5px;
        color: #15d36a;
    }
    .name1{
          position: relative;
           color: #15d36a;
         span{
           color: #15d36a;
         }
    }
   }
   .two{
     display: flex;
     width: 280px;
    //  margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #e23c2e !important;
        text-align: center;
        line-height: 20px;
        color: #e23c2e;
    }
    .name{
        margin-left:5px;
        color: #e23c2e;
    }
    .name1{
          position: relative;
           color: #e23c2e;
         span{
           color: #e23c2e;
         }
    }
   }
   .one{
    display: flex;
    width: 280px;
    margin-right: 1px;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid grey;
        text-align: center;
        line-height: 20px;
    }
    .name{
        margin-left:5px;
    }
    .name1{
          position: relative;
          margin-left:5px;
       
    }
 
   }
}
 </style>