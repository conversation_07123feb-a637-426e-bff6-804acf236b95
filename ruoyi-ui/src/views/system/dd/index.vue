<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="90px"
    >
      <el-form-item label="任务名称" prop="rwmc">
        <el-input
          v-model="queryParams.rwmc"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="gsxm">
        <el-select
          v-model="queryParams.gsxm"
          placeholder="请选择所属项目"
          clearable
        >
          <el-option
            v-for="dict in dict.type.xmmc"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <!-- <el-input
          v-model="queryParams.gsxm"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
      </el-form-item>
      <el-form-item label="时间范围">
        <div class="block">
          <el-date-picker
            v-model="queryParams.time"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :unlink-panels="true"
            :default-time="['00:00:00', '23:59:59']"
          >
            >
          </el-date-picker>
        </div>
      </el-form-item>
      <!-- <el-form-item label="任务类型" prop="rwlx">
        <el-select v-model="queryParams.rwlx" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.dd_rwlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="是否加急" prop="sfjj">
        <el-select v-model="queryParams.sfjj" placeholder="请选择是否加急" clearable>
          <el-option
            v-for="dict in dict.type.dd_sfjj"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="订单状态" prop="ddzt">
        <el-select
          v-model="queryParams.ddzt"
          placeholder="请选择订单状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.dd_ddzt"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="联系人姓名 " prop="lxrxm">
        <el-input
          v-model="queryParams.lxrxm"
          placeholder="请输入联系人姓名 "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="联系电话" prop="lxrdh">
        <el-input
          v-model="queryParams.lxrdh"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:dd:export']"
          >导出订单</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="exportGz"
          >导出工资</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="ddList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="订单编号"
        align="center"
        prop="ddbh"
        width="165"
      />
      <el-table-column label="联系人姓名 " align="center" prop="lxrxm" />
      <el-table-column label="任务名称" align="center" prop="rwmc" />
      <el-table-column label="任务类型" align="center" prop="rwlx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dd_rwlx" :value="scope.row.rwlx" />
        </template>
      </el-table-column>
      <el-table-column label="所属项目" align="center" prop="gsxm" />
      <el-table-column label="是否加急" align="center" prop="sfjj">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dd_sfjj" :value="scope.row.sfjj" />
        </template>
      </el-table-column>

      <el-table-column label="发放时间" align="center" prop="gzffsj" />
      <el-table-column label="劳务费金额" align="center" prop="gzzj" />
      <el-table-column label="订单状态" align="center" prop="ddzt">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dd_ddzt" :value="scope.row.ddzt" />
        </template>
      </el-table-column>

      <el-table-column label="联系人电话 " align="center" prop="lxrdh" />
      <el-table-column label="发布时间" align="center" prop="cjsj" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.cjsj }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.ddzt == 1"
            @click="handleExame(scope.row)"
            v-hasPermi="['system:dd:ddsh']"
            >订单审核</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:dd:queryXq']"
            >订单详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleUser(scope.row)"
            v-if="scope.row.ddzt > 4"
            v-hasPermi="['system:dd:cymdList']"
            >参与名单</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-if="scope.row.ddzt > 4"
            @click="handleSend(scope.row)"
            v-hasPermi="['system:dd:gzlb']"
            >劳务费发放</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="总金额" prop="zje">
          <el-input v-model="form.zje" placeholder="请输入总金额" />
        </el-form-item>
        <el-form-item label="定金金额" prop="xfdj">
          <el-input v-model="form.xfdj" placeholder="请输入定金金额" />
        </el-form-item>
        <el-form-item label="尾款金额" prop="xfwk">
          <el-input v-model="form.xfwk" placeholder="请输入尾款金额" />
        </el-form-item>
        <el-form-item label="任务单价" prop="rwdj">
          <el-input v-model="form.rwdj" placeholder="请输入任务单价" />
        </el-form-item>
        <el-form-item label="订单状态" prop="ddzt">
          <el-select v-model="form.ddzt" placeholder="请选择订单状态">
            <el-option
              v-for="dict in dict.type.dd_ddzt"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参与人数" prop="cyrs">
          <el-input v-model="form.cyrs" placeholder="请输入参与人数" />
        </el-form-item>
        <el-form-item label="是否为草稿" prop="sfcg">
          <el-input v-model="form.sfcg" placeholder="请输入是否为草稿" />
        </el-form-item>
        <el-form-item label="联系人姓名 " prop="lxrxm">
          <el-input v-model="form.lxrxm" placeholder="请输入联系人姓名 " />
        </el-form-item>
        <el-form-item label="联系电话" prop="lxrdh">
          <el-input v-model="form.lxrdh" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="着装要求" prop="zzyq">
          <el-input
            v-model="form.zzyq"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="打卡类型" prop="dklx">
          <el-select v-model="form.dklx" placeholder="请选择打卡类型">
            <el-option
              v-for="dict in dict.type.dd_dklx"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 审核订单弹窗 -->
    <el-dialog
      title="订单审核"
      :visible.sync="handleExameModel"
      width="34rem"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="8rem">
        <el-form-item label="是否通过">
          <el-radio-group v-model="labels">
            <el-radio :label="0">通过</el-radio>
            <el-radio :label="1">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核驳回原因" prop="lysj" v-if="labels">
          <el-input
            v-model="form.lysj"
            type="textarea"
            :style="{ width: '100%' }"
            :autosize="{ minRows: 10, maxRows: 10 }"
            placeholder="请输入审核驳回原因"
          />
        </el-form-item>
        <el-form-item
          label="审核类型"
          prop="ddtype"
          v-if="!labels && type == 1"
        >
          <el-select
            v-model="form.ddtype"
            placeholder="请选择审核类型"
            :style="{ width: '100%' }"
          >
            <el-option
              v-for="(item, index) in ddtype"
              :key="index"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormyx">确 定</el-button>
        <el-button @click="handleExameModel = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDd,
  getDd,
  delDd,
  addDd,
  updateDd,
  listDdExame,
  getDdddLx,
} from "@/api/system/dd";

export default {
  inject: ["reload"],
  name: "Dd",
  dicts: ["dd_rwlx", "dd_ddzt", "dd_dklx", "dd_sfjj", "xmmc"],
  data() {
    return {
      // 审核订单
      handleExameModel: false,
      handleExameVal: null,
      labels: 0,
      type: 0,
      ddtype: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      ddList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rwmc: null,
        rwlx: null,
        sfjj: null,
        ddzt: null,
        lxrxm: null,
        lxrdh: null,
        time: "",
        beginTime: "",
        endTime: "",
        gsxm: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        lysj: [
          { required: true, message: "审核原因不能为空", trigger: "blur" },
        ],
        rwmc: [
          { required: true, message: "任务名称不能为空", trigger: "blur" },
        ],
        lxrxm: [
          { required: true, message: "联系人姓名 不能为空", trigger: "blur" },
        ],
        ddtype: [
          { required: true, message: "审核类型不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listDd({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        rwmc: this.queryParams.rwmc,
        beginTime: this.queryParams.time ? this.queryParams.time[0] : "",
        endTime: this.queryParams.time ? this.queryParams.time[1] : "",
        ddzt: this.queryParams.ddzt,
        lxrxm: this.queryParams.lxrxm,
        gsxm: this.queryParams.gsxm,
      }).then((response) => {
        this.ddList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ddbh: null,
        userId: null,
        rwmc: null,
        rwlx: null,
        sfjj: null,
        rwdz: null,
        rwjd: null,
        rwwd: null,
        rwbz: null,
        sfxyzd: null,
        dzUserId: null,
        zmEwm: null,
        kqEwm: null,
        rwrs: null,
        zje: null,
        xfdj: null,
        xfwk: null,
        rwdj: null,
        ddzt: null,
        cyrs: null,
        sfcg: null,
        lxrxm: null,
        lxrdh: null,
        zzyq: null,
        dklx: null,
        dkjl: null,
        dzQkzj: null,
        cjsj: null,
        delFlag: null,
        lysj: null,
        ddtype: null,
        gsxm: null,
        // time:[],
        // beginTime:'',
        // endTime:''
      };
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        rwmc: "",
        beginTime: "",
        endTime: "",
        ddzt: "",
        lxrxm: "",
        time: "",
        gsxm: "",
      };
      this.resetForm("form");
    },

    // 劳务费发放
    handleSend(val) {
      this.$router.push({
        path: "/GzExame",
        query: {
          id: val.id,
          xmmc: val.rwmc,
        },
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        rwmc: "",
        beginTime: "",
        endTime: "",
        ddzt: "",
        lxrxm: "",
        time: "",
        gsxm: "",
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单";
    },
    // 审核订单
    async handleExame(val) {
      this.ddtype = [];
      this.type = Number(val.ddlx);
      const res = await getDdddLx();
      res.data.map((item) => {
        this.ddtype.push({
          value: item.lxDm,
          label: item.lxMc,
        });
      });
      this.handleExameModel = true;
      this.handleExameVal = val;
    },
    /* 订单详情 */
    handleDetail(val) {
      this.$router.push({
        path: "/ddxq",
        query: {
          id: val.id,
        },
      });
    },
    /* 参与名单 */
    handleUser(val) {
      this.$router.push({
        path: "/cymd",
        query: {
          id: val.id,
        },
      });
    },
    async submitFormyx() {
      // shzt 1审核通过  2审核失败
      var res = await listDdExame({
        id: this.handleExameVal.id,
        shzt: this.labels == 0 ? 1 : 2,
        sbxx: this.form.lysj,
        shdm: this.form.ddtype,
      });
      this.handleExameModel = false;
      this.$modal.msgSuccess(`${res.msg}`);
      this.reload();
      this.resetQuery();
      this.form.lysj = "";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDd(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDd(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDd(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除订单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDd(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/dd/export",
        {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          rwmc: this.queryParams.rwmc,
          beginTime: this.queryParams.time ? this.queryParams.time[0] : "",
          endTime: this.queryParams.time ? this.queryParams.time[1] : "",
          ddzt: this.queryParams.ddzt,
          lxrxm: this.queryParams.lxrxm,
          gsxm: this.queryParams.gsxm,
        },
        `dd_${new Date().getTime()}.xlsx`
      );
    },
    /** 导出工资 */
    exportGz() {
      this.download(
        "system/dd/exportGz",
        {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          rwmc: this.queryParams.rwmc,
          beginTime: this.queryParams.time ? this.queryParams.time[0] : "",
          endTime: this.queryParams.time ? this.queryParams.time[1] : "",
          ddzt: this.queryParams.ddzt,
          lxrxm: this.queryParams.lxrxm,
        },
        `dd_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
