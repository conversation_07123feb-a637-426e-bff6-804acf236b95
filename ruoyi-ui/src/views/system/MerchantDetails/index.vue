<!--
 * @Description: 
 * @Author: 小刘
 * @Date: 2025-03-31 08:45:45
 * @LastEditTime: 2025-03-31 14:25:09
 * @LastEditors: 小刘
-->
<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="变动类型" prop="szlx">
        <el-select v-model="queryParams.szlx" placeholder="请选择变动类型" clearable>
          <el-option
            v-for="dict in dict.type.user_qb_szlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账变时间">
        <el-date-picker
          v-model="queryParams.cjsj"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="变动方式" prop="lzlx">
        <el-select v-model="queryParams.lzlx" placeholder="请选择变动方式" clearable>
          <el-option
            v-for="dict in dict.type.user_qb_lzlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="qbMxList" @selection-change="handleSelectionChange">
      <el-table-column label="账变时间" align="center" prop="cjsj" />
      <el-table-column label="变更金额" align="center" prop="xgje" />
      <el-table-column label="商户额" align="center" prop="ye" />
      <el-table-column label="变动类型" align="center" prop="szlx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qb_szlx" :value="scope.row.szlx"/>
        </template>
      </el-table-column>
     
     
      <el-table-column label="变动方式" align="center" prop="lzlx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qb_lzlx" :value="scope.row.lzlx"/>
        </template>
      </el-table-column>
       <el-table-column label="备注" align="center" prop="bz" />
       
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    </div>
</template>
<script>
import {listDetail } from "@/api/system/MerchantDetails";

export default {
  name: "MerchantDetails",
  dicts: ['user_qb_lzlx', 'user_qb_szlx'],
  data() {
    return {
        // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 明细表格数据
      qbMxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lzlx:'',
        szlx:'',
        cjsj:[]
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      } 
    }
  
  },
  created() {
    this.getList();
  },
  methods:{
   async getList(){
    this.loading = true;
    
    const res= await listDetail({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        lzlx:this.queryParams.lzlx,
        szlx:this.queryParams.szlx,
        beginCjsj:this.queryParams.cjsj?this.queryParams.cjsj[0]:'',
        endCjsj:this.queryParams.cjsj?this.queryParams.cjsj[1]:'',
    })
        this.qbMxList = res.rows;
        this.total = res.total;
        this.loading = false;
    },
    // 表单重置
    reset() {
      this.form = {
        pageNum: 1,
        pageSize: 10,
        lzlx:'',
        szlx:'',
        cjsj:''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCjsj = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
  }
}
</script>