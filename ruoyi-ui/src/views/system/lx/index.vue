<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="审核代码" prop="lxDm">
        <el-input
          v-model="queryParams.lxDm"
          placeholder="请输入审核代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核名称" prop="lxMc">
        <el-input
          v-model="queryParams.lxMc"
          placeholder="请输入审核名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:lx:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:lx:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lxList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="审核代码" align="center" prop="lxDm" />
      <el-table-column label="审核名称" align="center" prop="lxMc" />
      <el-table-column label="创建时间" align="center" prop="cjsj" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.cjsj }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:lx:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:lx:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改审核类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="105px">
        <el-form-item label="审核名称" prop="lxMc">
          <el-input v-model="form.lxMc" placeholder="请输入审核名称" />
        </el-form-item>
      <el-row v-for="(item,index) in form.Addprocess" :key='index'>
        <el-col :span="6">
          <div class="">
            <el-form-item label="子流程名称">
                <el-input v-model="item.processName"  disabled/>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="">
            <el-form-item 
            :key="item.key"
             label="审核人姓名" 
            :prop="'Addprocess.'+index+'.Name'"
            :rules="{ required: true, message: '审核人姓名不能为空', trigger: 'blur' }" >
            <el-select
              v-model="item.Name"
              placeholder="请选择审核人"
              :style="{ width: '100%' }"
            >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.xm"
              :value="item.id"
             
            >
            </el-option>
          </el-select>
        </el-form-item>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="">
            <el-form-item label="审核人类型" 
            :key="item.key"
            :prop="'Addprocess.'+index+'.Type'"
            :rules="{ required: true, message: '审核人类型不能为空', trigger: 'blur' }" 
            >
            <el-select
              v-model="item.Type"
              placeholder="请选择审核人类型"
              :style="{ width: '100%' }"
            >
            <el-option
              v-for="(item, index) in dict.type.sys_shlx"
              :key="index"
              :label="item.label"
              :value="item.label"
             
            >
            </el-option>
          </el-select>
        </el-form-item>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="" style="margin-left: 10%;">
            <el-button 
            type="primary" 
            icon="el-icon-plus" 
            circle 
            v-if="index==form.Addprocess.length-1"
            @click="add(index)"
            ></el-button>
            <el-button 
            type="primary" 
            icon="el-icon-minus" 
            circle 
            v-else
            @click="del(index)"></el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
 
    </el-dialog>
  </div>
</template>

<script>
import { listLx, getLx, delLx, addLx, updateLx,userList } from "@/api/system/lx";

export default {
  inject: ["reload"],
  name: "Lx",
  dicts: ['sys_shlx'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 审核类型表格数据
      lxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lxDm: null,
        lxMc: null,
      },
      userList:[],
      // 表单参数
      form: {
        lxMc:'',
        Addprocess:[
          {
            processName:'申请人1',
            Name:'',
            Type:''
          }
        ],
      },
      flagedit:false,//修改操作
      data:'',//修改返回数据
      // 表单校验
      rules: {
        lxDm: [
          { required: true, message: "审核代码不能为空", trigger: "blur" }
        ],
        lxMc: [
          { required: true, message: "审核名称不能为空", trigger: "blur" }
        ],
       
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询审核类型列表 */
    getList() {
      this.loading = true;
      listLx(this.queryParams).then(response => {
        this.lxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        lxDm: null,
        lxMc: null,
        cjsj: null,
        delFlag: null,
        Addprocess:[
          {
            processName:'申请人1',
            Name:'',
            Type:''
          }
        ],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
   async handleAdd() {
      const {data:res}=await userList()
      this.userList=res
      this.reset();
      this.open = true;
      this.title = "添加审核类型";
    },
    /** 修改按钮操作 */
   async handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      const {data:res}=await userList()
      this.userList=res
      getLx(id).then(response => {
        this.form.lxMc = response.data.lxMc;
        this.data=response.data
        this.form.Addprocess=response.data.lxbzList.map(
          ({sprId,sprLx},index)=>
            ({
              Name:sprId,
              Type:sprLx,
              processName:'申请人'+`${index+1}`
            }))
        this.flagedit=true
        this.open = true;
        this.title = "修改审核类型";
      });
    },


    // 添加按钮操作
    add(index) {
      this.form.Addprocess.push({
        processName:'申请人'+`${index+2}`,
        Name:'',
        Type:''
      })
    },
    /** 删除按钮 */
    del(index) {
      this.form.Addprocess.splice(index,1)
      this.form.Addprocess.map((item,index)=>{
        item.processName='申请人'+`${index+1}`
      })
      this.$forceUpdate()
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(async(valid) => {
        if (valid) {
          let ArrayData=this.form.Addprocess.map(
              ({ Type, Name })=>({
                sprId: Name,
                sprLx:Type
              }) 
            )
          if (this.flagedit==true) {
            const result=await updateLx({
              id:this.data.id,
              lxDm:this.data.lxDm,
              lxMc:this.form.lxMc,
              lxbzList:ArrayData
            })
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          } else {
            const res=await addLx({
              id:this.data.id,
              lxDm:this.data.lxDm,
              lxMc:this.form.lxMc,
              lxbzList:ArrayData
            }) 
            if(res.code==200){
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除审核类型编号为"' + ids + '"的数据项？').then(function() {
        return delLx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/lx/export', {
        ...this.queryParams
      }, `lx_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
