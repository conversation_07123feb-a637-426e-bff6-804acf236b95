<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="4.25rem">
      <el-form-item label="审核状态" prop="shzt">
        <el-select v-model="queryParams.shzt" placeholder="请选择审核状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_shzt"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-button style="float: right;" type="text" @click="back"
              >返回列表</el-button>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:kqmx:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleTestMore"
          v-hasPermi="['system:kqmx:export']"
        >批量审核</el-button>
      </el-col>
      <el-col
        :span="1.5"
        style="color: red; margin-left: 1%; font-weight: 600;margin-top:0.2%" >
      <el-link icon="el-icon-edit" type="danger" style="color: red;font-weight: 600;">备注  :  {{ mark||"无" }} </el-link>
    </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
       v-loading="loading"
       :data="kqmxList"
       @selection-change="handleSelectionChange"
       :span-method="objectSpanMethod"
       ref="tableRefName"
       border
       style="margin-bottom:20rpx;"
       :cell-style="tableRowStyle"
       >
      <el-table-column type="selection" width="55" align="center" :selectable='selectEnable'/>
      <!-- <el-table-column label="序号" align="center" prop="id" width="55"/> -->
      <el-table-column label="订单编号" align="center" prop="ddId" width="175" />
      <el-table-column label="项目名称" align="center" prop="xmmc" />
      <el-table-column label="人员姓名" align="center" prop="ryxm"  width="95" />
      <el-table-column label="日期" align="center" prop="rq" />
      <el-table-column label="实发劳务费" align="center" prop="sfgz" />
      <el-table-column label="打卡类型" align="center" prop="dklx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_dklx" :value="scope.row.dklx"/>
        </template>
      </el-table-column>
      <el-table-column label="规定签到时间" align="center" prop="gdqdsj" />
      <el-table-column label="实际签到时间" align="center" prop="sjqdsj"  />
      <!-- <el-table-column label="考勤信息" align="center" prop="kqzt" /> -->
      <el-table-column label="考勤备注" align="center" prop="kqbz" width='300px'/>
      <el-table-column label="审核状态 " align="center" prop="shzt">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_shzt" :value="scope.row.shzt" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleTest(scope.row)"
            v-hasPermi="['system:kqmx:remove']"
          >审核</el-button>
           <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleset(scope.row)"
            v-hasPermi="['system:kqmx:remove']"
          >操作</el-button> 
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 审核备注 -->
    <div style="margin-top:20px;" class="shstep">
    <div class="kqstep" >
      <div  
      v-for="(item,index) in kqlist" 
      :key='index' 
      :class='item.auditStatus==2?"two":(item.auditStatus==3?"three":"one")' 
      @click="shSatus(index)">
        <div class="yuan" >{{index+1}}</div>
        <div class='name' >审批人{{index+1}}:</div>
        <div class="name1" >{{item.auditUserNo}}
            <span style="font-size:11px;">{{item.auditRemark?'(有备注)':'(无备注)'}}</span>
            <span> ---------></span>
        </div>
         <el-dialog title="考勤审核" :visible.sync="showkqbz" width="31.25rem" append-to-body >
          <span style="font-size:16px;margin-right:3px;">备注</span>{{kqlist[kqindex].auditRemark||"无"}}
         </el-dialog>
      </div>
    </div>
    </div>
 <!-- 审核弹窗 -->
 <el-dialog title="考勤审核" :visible.sync="openshow" width="31.25rem" append-to-body>
       <el-form ref="form" :model="form" :rules="rules" label-width="5rem">
         <el-form-item label="核对状态" >
           <el-radio-group v-model="form.gzhdzt">
             <el-radio
               v-for="dict in gzhdzt"
               :key="dict.key"
               :label="parseInt(dict.key)"
             >{{dict.label}}</el-radio>
           </el-radio-group>
         </el-form-item>
        <el-form-item label="考勤备注" prop="bz" >
          <el-input v-model="form.bz" type="textarea" :style="{width: '100%'}" placeholder="请输入考勤备注" />
        </el-form-item>
         <!-- <el-form-item label="审批人" prop="sp" >
          <div class="btImg">
                <div class="block" v-for="(item,index) in arr" :key='index'><el-avatar :size="50" :src="item.circleUrl"></el-avatar>
                <div class="plus" v-if="index==arr.length-1"><el-button icon="el-icon-plus" circle @click="addbtn"></el-button></div>
                <div class="plus" style="margin-left:-1px;"  v-if="index==arr.length-1"><el-button icon="el-icon-minus" circle @click="delbtn(index)"></el-button></div>
                </div>
          </div>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="openshow=false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="考勤审核" :visible.sync="showopen" width="40rem" height='40rem' append-to-body>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showopen=false">确 定</el-button>
        <el-button @click="showopen=false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import { listKqmx, getKqmx, delKqmx, shKqMxKqmx ,KqmxRemark} from "@/api/system/kqmx";
// import{  SearchMark} from "@/api/system/gzlb";
export default {
  name: "Kqmx",
  dicts: ['sys_shzt','sys_dklx'],
  data() {
    return {
     active: 0,
      // 遮罩层
      mark:'',
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考勤明细表格数据
      kqmxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      id: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        ddId: this.$route.query.id,
        shzt: null,
      },
      gzhdzt:[
        {
          key:0,
          label:"通过"
        },
        {
          key:2,
          label:"未通过"
        },
      ],
      // 表单参数
      form: {},
      ddId:'',
      ddid:[],
      openshow:false,
      jin:true,
      // 表单校验
      rules: {},
      spanArr:[],
      pos:0,
      openid:'',
      showContent:false,
      arr:[{
         circleUrl: "https://cube.elemecdn.com/3/7c/********************************.png",
      }],
      showopen:false,
      kqlist:'',
      kqindex:0,
      showkqbz:false,
    };
  },
  created(){
    this.getList()
  },
  activate() {
     this.getList()
   },
  methods: {
    tableRowStyle({ row, column, rowIndex, columnIndex }){
      if(row.sfbz===true&& columnIndex === 8){
      return 'color:red; font-weight:900;!important;'
	}
    },
   shSatus(index){
    this.kqindex=index
    this.showkqbz=true
   },
   addbtn(){
      this.showopen=true
      this.arr.push({
        circleUrl:"https://cube.elemecdn.com/3/7c/********************************.png",
      })
   },
   delbtn(index){
    if(this.arr.length==0){
      this.$modall.msgErr('不可以删除！')
      return 
    }
    this.arr.splice(index,1)
   },
    /** 查询考勤明细列表 */
    async getList() {
      let result=await SearchMark({ddId:this.queryParams.ddId})
      this.mark=result.data?result.data.ddbz:''
      this.pos=0;
      this.spanArr=[];
      this.kqmxList=[];
      this.loading = true;
      var res= await  listKqmx({ddId:this.$route.query.id,shzt:this.queryParams.shzt})
      this.kqmxList = res.data;
        let {data:Res }= await KqmxRemark({flowNo:this.$route.query.kq})
        this.kqlist=Res
        this.loading = false;
         this.kqmxList.map((item,index)=>{
           if(index===0){
             this.spanArr.push(1)
             this.pos = 0
           }else{
             if (this.kqmxList[index].openid === this.kqmxList[index - 1].openid&&
                 this.kqmxList[index].dklx === 2) {
                     this.spanArr[this.pos] += 1;
                     this.spanArr.push(0)
                   }
                   else {
                     this.spanArr.push(1);
                     this.pos = index;
                   }
           }
         })

    },
    selectEnable(row, rowIndex){//审核通过禁用
      if(row.shzt!=0){
        return true
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0||columnIndex === 3 || columnIndex === 4 ||columnIndex === 5 ||columnIndex === 10||columnIndex === 11||columnIndex === 12||columnIndex === 9) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row, colspan: _col
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ddId: null,
        xmmc: null,
        ryxm: null,
        rq: null,
        dklx: null,
        gdqdsj: null,
        sjqdsj: null,
        kqzt: null,
        kqbz: null,
        shzt: null
      };
      this.resetForm("form");
    },
    back(){
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(item => item.path === this.$route.path), 1)
      this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews.length-1].path)
      this.$destroy()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {

      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考勤明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getKqmx(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考勤明细";
      });
    },
    handleTestMore(){
      this.reset();
      this.openshow=true
    },
    handleTest(row){//审核
      // this.ddId=row.ddId
      // this.openshow=true
      // console.log(row)
      // this.openid=row.openid
      if(row.shzt!=0){
        this.reset();
        this.id = row.id || this.ids
        this.ddId=row.ddId
        this.openshow=true
        this.openid=row.openid
      }else{
        this.$modal.msgWarning("不可重复审核！")
      }
    },
    /** 提交按钮 */
    submitForm() {
      if(this.ids.length>0){
        shKqMxKqmx({id:item,shzt:this.form.gzhdzt,kqbz:this.form.bz,ddId:this.ddId,openid:this.openid}).then(response => {
          this.getList();
        })
      }else{
        shKqMxKqmx({shzt:this.form.gzhdzt,kqbz:this.form.bz,ddId:this.ddId,openid:this.openid}).then(response => {
          this.getList();
        });
      }
      this.$modal.msgSuccess("提交审核成功");
      this.openshow = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除考勤明细编号为"' + ids + '"的数据项？').then(function() {
        return delKqmx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/kqmx/export', {
        ...this.queryParams

      }, `kqmx_${new Date().getTime()}.xlsx`)
      console.log(this.queryParams)
    }
  }
};
</script>
<style lang="scss" scoped>
.kqstep{
   display: flex;
   justify-content: center;
   flex-wrap: wrap;
   width: 100%;
   min-height: 180px;
   font-size: 17px;
   font-weight:600;
   .c1{//审核中
     display: flex;
     width: 280px;
    //  margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #ffbe3c !important;
        text-align: center;
        line-height: 20px;
        color: #ffbe3c;
    }
    .name{
        margin-left:5px;
        color: #ffbe3c;
    }
    .name1{
          position: relative;
           color: #ffbe3c;
         span{
           color: #ffbe3c;
         }
    }
}
  .three{
     display: flex;
     width: 280px;
      // margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #15d36a !important;
        text-align: center;
        line-height: 20px;
        color: #15d36a;
    }
    .name{
        margin-left:5px;
        color: #15d36a;
    }
    .name1{
          position: relative;
           color: #15d36a;
         span{
           color: #15d36a;
         }
    }
   }
   .two{
     display: flex;
     width: 280px;
    //  margin-left: -1%;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #e23c2e !important;
        text-align: center;
        line-height: 20px;
        color: #e23c2e;
    }
    .name{
        margin-left:5px;
        color: #e23c2e;
    }
    .name1{
          position: relative;
           color: #e23c2e;
         span{
           color: #e23c2e;
         }
    }
   }
   .one{
    display: flex;
    width: 280px;
    margin-right: 1px;
    .yuan{
        margin-top:2px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid grey;
        text-align: center;
        line-height: 20px;
    }
    .name{
        margin-left:5px;
    }
    .name1{
          position: relative;
          margin-left:5px;
       
    }
 
   }
}
.btImg{
   display: flex;
    .block{
          display: flex;
          margin-top: 30px;

      .plus{
        margin-top: 10px;
        width: 45px;
        margin-left: 10px;
      }
    }
}
</style>