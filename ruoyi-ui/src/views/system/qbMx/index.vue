<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="收支类型" prop="szlx">
        <el-select v-model="queryParams.szlx" placeholder="请选择收支类型" clearable>
          <el-option
            v-for="dict in dict.type.user_qb_szlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCjsj"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="流转类型" prop="lzlx">
        <el-select v-model="queryParams.lzlx" placeholder="请选择流转类型" clearable>
          <el-option
            v-for="dict in dict.type.user_qb_lzlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:qbMx:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:qbMx:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:qbMx:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:qbMx:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="qbMxList" @selection-change="handleSelectionChange">
      <el-table-column label="姓名" align="center" prop="xm" />
      <el-table-column label="修改金额" align="center" prop="xgje" />
      <el-table-column label="余额" align="center" prop="ye" />
      <el-table-column label="收支类型" align="center" prop="szlx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qb_szlx" :value="scope.row.szlx"/>
        </template>
      </el-table-column>
     
     
      <el-table-column label="流转类型" align="center" prop="lzlx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_qb_lzlx" :value="scope.row.lzlx"/>
        </template>
      </el-table-column>
       <el-table-column label="备注" align="center" prop="bz" />
       <el-table-column label="创建时间" align="center" prop="cjsj" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.cjsj }}</span>
        </template>
      </el-table-column>
      
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:qbMx:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:qbMx:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQbMx, getQbMx, delQbMx, addQbMx, updateQbMx } from "@/api/system/qbMx";

export default {
  name: "QbMx",
  dicts: ['user_qb_lzlx', 'user_qb_szlx'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 明细表格数据
      qbMxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 订单编号时间范围
      daterangeCjsj: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: this.$route.query.id,
        xgje: null,
        ye: null,
        szlx: null,
        cjsj: null,
        lzlx: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCjsj && '' != this.daterangeCjsj) {
        this.queryParams.params["beginCjsj"] = this.daterangeCjsj[0];
        this.queryParams.params["endCjsj"] = this.daterangeCjsj[1];
      }
      listQbMx(this.queryParams).then(response => {
        this.qbMxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
     
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: this.$route.query.id,
        xgje: null,
        ye: null,
        szlx: null,
        cjsj: null,
        bz: null,
        lzlx: null,
        sf: null,
        ddbh: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCjsj = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getQbMx(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateQbMx(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQbMx(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除明细编号为"' + ids + '"的数据项？').then(function() {
        return delQbMx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/qbMx/export', {
        ...this.queryParams
      }, `qbMx_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
