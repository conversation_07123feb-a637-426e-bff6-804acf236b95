<template>
    <div class="app-container">
      <el-card>
        <div slot="header" class="clearfix">
          <span>基本信息</span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            @click="back"
            >返回列表</el-button
          >
        </div>
        <el-descriptions
          :label-style="{ width: '10%' }"
          :content-style="{ width: '15%' }"
          :column="4"
          border
        >
          <el-descriptions-item label="项目名称">{{
            list.rwmc||''
          }}</el-descriptions-item>
          <el-descriptions-item label="订单号码">{{
            list.ddbh||''
          }}</el-descriptions-item>
     
          <el-descriptions-item label="联系人">{{
            list.lxrxm||''
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            list.lxrdh||''
          }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{
            list.cjsj||''
          }}</el-descriptions-item>
          <el-descriptions-item label="服务性质">{{
            list.rwlx == 1 ? "贴身保护" : list.fwxz == 2 ? "秩序维护" : list.fwxz == 3 ? "安检" : "维稳"
          }}</el-descriptions-item>
          
          <el-descriptions-item label="所需人数">{{
            list.rwrs + "人"
          }}
           <el-link type="primary" :underline="false" style="margin-left: 6px;">({{ '男'+males||""+'人' }} /  </el-link>
           <el-link type="primary" :underline="false">{{ '女'+females||""+'人' }} ) </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="年龄">
            {{ list.ddRyyq.zxnl? list.ddRyyq.zxnl:'' }}
            {{Number(list.ddRyyq.nlxz)==0?'不限制':'--'}}
            {{ list.ddRyyq.zdnl?list.ddRyyq.zdnl :'' }}
            {{Number(list.ddRyyq.nlxz)==0?'':'岁'}}
            </el-descriptions-item>
          
          <el-descriptions-item label="身高">{{
            list.ddRyyq.sgxz==0?
            '160':list.ddRyyq.sgxz==1?
            '165':list.ddRyyq.sgxz==2?
            '170':list.ddRyyq.sgxz==3?
            '175':list.ddRyyq.sgxz==4?
            '180':list.ddRyyq.sgxz==5? 
            '185':'不限制'
           
             
          }}
           {{  list.ddRyyq.sgxz==6?'':"cm以上" }}
        </el-descriptions-item>
          <el-descriptions-item label="等级">{{
            list.rwlx == 1
              ? "vip1"
              : list.rwlx == 2
              ? "vip2"
              : list.rwlx == 3
              ? "vip3"
              : list.rwlx == 4
              ? "vip4"
              : list.rwlx == 5
              ? "vip5"
              : list.rwlx == 6
              ? "vip6"
              : list.rwlx == 7
              ? "vip7"
              :''
          }}</el-descriptions-item>
  
          <el-descriptions-item label="是否加急">{{
            list.sfjj == 0 ? "加急" : "不加急"
          }}</el-descriptions-item>
           '0取消','1待审核','2审核拒绝','3待付','4招募中','5进行中','6待结算','7待评价',8'已完成'",
          <el-descriptions-item :span="2" label="订单状态">{{
            list.ddzt == 0
              ? "取消"
              : list.ddzt == 1
              ? "待审核"
              : list.ddzt == 2
              ? "审核拒绝"
              : list.ddzt == 3
              ? "待付款"
              : list.ddzt == 4
              ? "招募中"
              : list.ddzt == 5
              ? "进行中"
              : list.ddzt == 6
              ? "待结算"
              : list.ddzt == 7
              ? "待评价"
              : list.ddzt == 8
              ? "已完成"
              : ""
          }}</el-descriptions-item>
          <el-descriptions-item :span="2" label="服务周期">
            <keep-alive>
              <el-button type="text" class="orderTimeDetail" @click="getDetail()">
                点击查看详情
              </el-button>
            </keep-alive>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="服务地址">
          {{list.rwdz||'' }}
        </el-descriptions-item>
          <el-descriptions-item label="着装要求" :span="4">
          {{list.zzyq||''}}
         </el-descriptions-item>
          <el-descriptions-item v-if="list.xddz" :span="4" label="队长情况总结">
           {{list.dzQkzj||''}}
          </el-descriptions-item>
          <el-descriptions-item v-if="list.xddz" :span="4" label="打卡类型">
           {{list.dklx=='1'?'队长打卡':list.dklx=='2'?'个人打卡':''}}
          </el-descriptions-item>
        </el-descriptions>
      
      </el-card>
      
      <el-card style="margin-top: 10px" header="费用信息">
       <!--  <el-card header="基本费用">
          <el-descriptions
            :label-style="{ width: '10%' }"
            :content-style="{ width: '40%' }"
            :column="2"
            border
          >
            <el-descriptions-item label="总金额">{{
              list.zje || 0
            }}</el-descriptions-item>
            <el-descriptions-item label="优惠金额">{{ 0 }}</el-descriptions-item>
            <el-descriptions-item
              label="已付金额"
              v-if="list.ddzt == 3 || list.ddzt > 3"
              >{{ list.yfdj }}</el-descriptions-item
            >
            <el-descriptions-item
              label="已付金额"
              v-if="list.ddzt == 2 || list.ddzt == 1 || list.ddzt == 0"
              >{{ 0 }}</el-descriptions-item
            >
            <el-descriptions-item
              label="未付金额"
              v-if="list.ddzt == 2 || list.ddzt == 1 || list.ddzt == 0"
              >{{ list.wfje }}</el-descriptions-item
            >
            <el-descriptions-item
              label="未付金额"
              v-if="list.ddzt == 3 || list.ddzt == 4 || list.ddzt == 5"
              >{{ list.wfje }}</el-descriptions-item
            >
            <el-descriptions-item
              label="未付金额"
              v-if="list.ddzt == 6 || list.ddzt == 7"
              >{{ 0 }}</el-descriptions-item
            >
            <el-descriptions-item
              label="尾款"
              v-if="list.ddzt == 6 || list.ddzt == 7"
              >{{ list.wk || 0 }}</el-descriptions-item
            >
            <el-descriptions-item v-if="list.yfje" label="定金金额">{{
              list.yfje || 0
            }}</el-descriptions-item>
          </el-descriptions>
        </el-card> -->
        <el-card style="margin-top: 10px" header="费用明细">
          <el-descriptions
          v-for="(item,index) in list.ddSfmxList"
          :key="index"
            :label-style="{ width: '25%',}"
            :content-style="{ width: '25%' }"
            :column="4"
            direction="vertical"
            border
            style="margin-bottom: 20px;"
          >
          <el-descriptions-item 
          :label-style="{ marginBottom: '20px'  }"
            label="收费类型 ">
            {{item.lx==1?
            '人员费用':item.lx==2?
            '设备费用':'加急费用' }}
            </el-descriptions-item>
            <el-descriptions-item 
            label="费用名称"
              >
              {{item.fymc || '' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="收费明细"
              >
              {{item.sldj || '' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="收费总价" >
              {{item.zj || '' }}
             
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card style="margin-top: 10px" header="设备列表">
          <el-descriptions
          v-for="(item,index) in list.ddSbyqList"
          :key="index"
            :label-style="{ width: '25%',}"
            :content-style="{ width: '25%' }"
            :column="4"
            direction="vertical"
            border
            style="margin-bottom: 20px;"
          >
          <el-descriptions-item 
          :label-style="{ marginBottom: '20px'  }"
            label="设备名称 ">
            {{item.sbmc || '' }}
            </el-descriptions-item>
            <el-descriptions-item 
            label="设备数量"
              >
              {{item.sxsl || '' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="设备单价"
              >
              {{item.sbdj || '' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="设备总结(单价*数量)" >
              {{item.sbzj || '' }}
             
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <div
          v-if="list.fblx == 1 && (list.ddzt == 0 || list.ddzt == 1)"
          class="btn"
        >
          <!-- 判断个人 -->
          <el-button size="small" type="primary" @click="confirmBtn">
            设置定金
          </el-button>
      
        </div>
  
        <div class="btn" v-if="list.ddzt == 6 || list.ddzt == 7">
          <el-button size="small" type="primary" @click="back">返回</el-button>
        </div>
        <div class="btn" v-if="list.ddzt == 2">
          <el-button size="small" type="primary" @click="Sure" >支付定金</el-button>
          <el-button size="small" type="primary" @click="back">返回</el-button>
        </div>
        <div class="btn" v-if="list.ddzt == 3">
          <el-button size="small" type="primary" @click="Send()">发布</el-button>
          <el-button size="small" type="primary" @click="back">返回</el-button>
        </div>
        <div class="btn" v-if="list.ddzt == 5 ">
          <el-button size="small" type="primary" @click="Surplus()">支付尾款</el-button>
          <el-button size="small" type="primary" @click="compliate()">完成</el-button>
        </div>
      </el-card>
      <Time 
      :data="Time" 
      :visible_Time='visible_Time'
      @cancelTime="cancelTime"
      @confirmTime="confirmTime"></Time>
      </div>
  </template>
  
  
  <script>
  import {
    getDdxq,
    dzTel,
    Send,
    uploadeReview,
    surePay,
    SurplusSet,
    examShlx,
    RequireFree,
  } from "@/api/system/dd";
 import Time from './Time.vue'
  export default {
    inject: ["reload"],
    components :{
      Time
  },
    data() {
      return {
      list:{},//列表详情
      females:0,//所需男生
      males:0,//所需女生,
      visible_Time:false,//服务周期
      Time:[],//服务周期

    }
    },
    created() {
      this.id = this.$route.query.id;
      this.getlist();
    },
    activated() {
      this.getlist();
    },
    methods: {
    
      getlist() {
        this.females=0
        this.males=0
        this.$nextTick(async () => {
          var res = await getDdxq({id:this.id});
          this.list = res.data;
          console.log(this.list,2222)
          res.data.ddFwzqList.map(item=>{
            this.females += item.sxrsnv
            this.males += item.sxrsnan

          })
        
        }); 
      },
      /* 服务周期 */
      getDetail(){
        this.Time=this.list.ddFwzqList
        this.visible_Time=true

      },
      cancelTime(){
        this.visible_Time=false
        this.Time=[]
      },
      confirmTime(){
        this.Time=[]
        this.visible_Time=false
      },
      back() {
        this.$store.state.tagsView.visitedViews.splice(
          this.$store.state.tagsView.visitedViews.findIndex(
            (item) => item.path === this.$route.path
          ),
          1
        );
        this.$router.push(
          this.$store.state.tagsView.visitedViews[
            this.$store.state.tagsView.visitedViews.length - 1
          ].path
        );
        this.$destroy();
      },
      
    
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .btn {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    .btn3 {
      display: flex;
      justify-content: center;
    }
    .btn1 {
      margin-right: 20px;
    }
  }
  </style>
  