<template>
    <div>
        <el-dialog title="服务周期" :visible.sync="visible_Time" >
             
          <el-descriptions
          v-for="(item,index) in data"
          :key="index"
            :label-style="{ width: '25%',}"
            :content-style="{ width: '25%' }"
            :column="4"
            direction="vertical"
            border
            style="margin-bottom: 20px;"
          >
          <el-descriptions-item 
          :label-style="{ marginBottom: '20px'  }"
            label="开始时间 ">
            {{item.kssj || '' }}
            </el-descriptions-item>
            <el-descriptions-item 
            label="结束时间"
              >
              {{item.jssj || '' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="所需人数(男生)"
              >
              {{item.sxrsnan || '0' }}
             
            </el-descriptions-item>
            <el-descriptions-item 
            label="所需人数(女生)" >
              {{item.sxrsnv || '0' }}
             
            </el-descriptions-item>
          </el-descriptions>
       
            <div slot="footer" class="dialog-footer">
            <el-button @click="cancelTime">取 消</el-button>
            <el-button type="primary" @click="confirmTime">确 定</el-button>
     </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props:{
    visible_Time :{
        type:Boolean,
    },
    data:{
        type:Array
    }
},
methods:{
    cancelTime(){
        this.$emit('cancelTime')
    },
    confirmTime(){
        this.$emit('confirmTime')
    }
}
}

</script>