<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="xm">
        <el-input
          v-model="queryParams.xm"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否通过" prop="sftg">
        <el-select v-model="queryParams.sftg" placeholder="请选择是否通过" clearable>
          <el-option
            v-for="dict in dict.type.user_xxjl_sftg"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCjsj"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="学习题目" prop="nrbh">
        <el-select v-model="queryParams.nrbh" placeholder="请选择学习题目" clearable>
          <el-option
            v-for="dict in dict.type.user_xxjl_nrbh"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="lx">
        <el-select v-model="queryParams.lx" placeholder="请选择类型" clearable>
          <el-option
            v-for="dict in dict.type.user_xxjl_xxlx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:xxjl:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="xxjlList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="用户姓名" align="center" prop="xm" />
      <el-table-column label="学习题目" align="center" prop="nrbh">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_xxjl_nrbh" :value="scope.row.nrbh"/>
        </template>
      </el-table-column>
      <el-table-column label="题数" align="center" prop="ts" />
      <el-table-column label="正确率" align="center" prop="zql" />
      <el-table-column label="是否通过" align="center" prop="sftg">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_xxjl_sftg" :value="scope.row.sftg"/>
        </template>
      </el-table-column>
      <el-table-column label="答题结束时间" align="center" prop="cjsj" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cjsj, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="lx">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_xxjl_xxlx" :value="scope.row.lx"/>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户学习记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="正确数量" prop="zqsl">
          <el-input v-model="form.zqsl" placeholder="请输入正确数量" />
        </el-form-item>
        <el-form-item label="错误数量" prop="cwsl">
          <el-input v-model="form.cwsl" placeholder="请输入错误数量" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="xm">
          <el-input v-model="form.xm" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label="unionid" prop="unionid">
          <el-input v-model="form.unionid" placeholder="请输入unionid" />
        </el-form-item>
        <el-form-item label="正确率" prop="zql">
          <el-input v-model="form.zql" placeholder="请输入正确率" />
        </el-form-item>
        <el-form-item label="是否通过" prop="sftg">
          <el-radio-group v-model="form.sftg">
            <el-radio
              v-for="dict in dict.type.user_xxjl_sftg"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学习题目" prop="nrbh">
          <el-select v-model="form.nrbh" placeholder="请选择学习题目">
            <el-option
              v-for="dict in dict.type.user_xxjl_nrbh"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="lx">
          <el-radio-group v-model="form.lx">
            <el-radio
              v-for="dict in dict.type.user_xxjl_xxlx"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listXxjl, getXxjl, delXxjl, addXxjl, updateXxjl } from "@/api/user/xxjl";

export default {
  name: "Xxjl",
  dicts: ['user_xxjl_xxlx', 'user_xxjl_sftg', 'user_xxjl_nrbh'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户学习记录表格数据
      xxjlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型时间范围
      daterangeCjsj: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xm: null,
        sftg: null,
        cjsj: null,
        nrbh: null,
        lx: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户学习记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCjsj && '' != this.daterangeCjsj) {
        this.queryParams.params["beginTime"] = this.daterangeCjsj[0];
        this.queryParams.params["endTime"] = this.daterangeCjsj[1];
      }
      listXxjl(this.queryParams).then(response => {

        // this.xxjlList = response.rows;
        this.xxjlList = response.rows.map(item => {
          const ts = item.zqsl + item.cwsl;
          // 使用Object.assign合并属性到新对象
          return Object.assign({}, item, { ts });
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        zqsl: null,
        cwsl: null,
        xm: null,
        unionid: null,
        zql: null,
        sftg: null,
        cjsj: null,
        nrbh: null,
        lx: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCjsj = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户学习记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getXxjl(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户学习记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateXxjl(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addXxjl(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除用户学习记录编号为"' + ids + '"的数据项？').then(function() {
        return delXxjl(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/xxjl/export', {
        ...this.queryParams
      }, `xxjl_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
